﻿/* ServicePro - Modern Professional Service Booking Platform */

/* CSS Custom Properties */
:root {
    /* Colors */
    --primary-50: #eff6ff;
    --primary-100: #dbeafe;
    --primary-500: #3b82f6;
    --primary-600: #2563eb;
    --primary-700: #1d4ed8;

    --gray-50: #f9fafb;
    --gray-100: #f3f4f6;
    --gray-200: #e5e7eb;
    --gray-300: #d1d5db;
    --gray-400: #9ca3af;
    --gray-500: #6b7280;
    --gray-600: #4b5563;
    --gray-700: #374151;
    --gray-800: #1f2937;
    --gray-900: #111827;

    --white: #ffffff;
    --success-500: #10b981;
    --warning-500: #f59e0b;
    --error-500: #ef4444;

    /* Typography */
    --font-family: 'Poppins', -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
    --font-size-xs: 0.75rem;
    --font-size-sm: 0.875rem;
    --font-size-base: 1rem;
    --font-size-lg: 1.125rem;
    --font-size-xl: 1.25rem;
    --font-size-2xl: 1.5rem;
    --font-size-3xl: 1.875rem;
    --font-size-4xl: 2.25rem;
    --font-size-5xl: 3rem;
    --font-size-6xl: 3.75rem;

    /* Spacing */
    --space-1: 0.25rem;
    --space-2: 0.5rem;
    --space-3: 0.75rem;
    --space-4: 1rem;
    --space-5: 1.25rem;
    --space-6: 1.5rem;
    --space-8: 2rem;
    --space-10: 2.5rem;
    --space-12: 3rem;
    --space-16: 4rem;
    --space-20: 5rem;
    --space-24: 6rem;

    /* Border Radius */
    --radius-sm: 0.375rem;
    --radius-md: 0.5rem;
    --radius-lg: 0.75rem;
    --radius-xl: 1rem;
    --radius-2xl: 1.5rem;
    --radius-full: 9999px;

    /* Shadows */
    --shadow-sm: 0 1px 2px 0 rgba(0, 0, 0, 0.05);
    --shadow-md: 0 4px 6px -1px rgba(0, 0, 0, 0.1), 0 2px 4px -1px rgba(0, 0, 0, 0.06);
    --shadow-lg: 0 10px 15px -3px rgba(0, 0, 0, 0.1), 0 4px 6px -2px rgba(0, 0, 0, 0.05);
    --shadow-xl: 0 20px 25px -5px rgba(0, 0, 0, 0.1), 0 10px 10px -5px rgba(0, 0, 0, 0.04);
    --shadow-2xl: 0 25px 50px -12px rgba(0, 0, 0, 0.25);

    /* Transitions */
    --transition-fast: 150ms ease-in-out;
    --transition-normal: 300ms ease-in-out;
    --transition-slow: 500ms ease-in-out;
}

/* Reset and Base Styles */
* {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
}

html {
    scroll-behavior: smooth;
    font-size: 16px;
}

body {
    font-family: var(--font-family);
    font-size: var(--font-size-base);
    line-height: 1.6;
    color: var(--gray-800);
    background-color: var(--white);
    -webkit-font-smoothing: antialiased;
    -moz-osx-font-smoothing: grayscale;
}

/* Typography */
h1, h2, h3, h4, h5, h6 {
    font-weight: 700;
    line-height: 1.2;
    color: var(--gray-900);
}

h1 { font-size: var(--font-size-5xl); }
h2 { font-size: var(--font-size-4xl); }
h3 { font-size: var(--font-size-3xl); }
h4 { font-size: var(--font-size-2xl); }
h5 { font-size: var(--font-size-xl); }
h6 { font-size: var(--font-size-lg); }

p {
    color: var(--gray-600);
    line-height: 1.7;
}

a {
    text-decoration: none;
    color: inherit;
    transition: var(--transition-fast);
}

img {
    max-width: 100%;
    height: auto;
}

/* Container */
.container {
    max-width: 1200px;
    margin: 0 auto;
    padding: 0 var(--space-6);
}

/* Navigation */
.navbar {
    position: fixed;
    top: 0;
    left: 0;
    right: 0;
    background: rgba(255, 255, 255, 0.95);
    backdrop-filter: blur(20px);
    border-bottom: 1px solid var(--gray-200);
    z-index: 1000;
    transition: var(--transition-normal);
}

.nav-content {
    display: flex;
    align-items: center;
    justify-content: space-between;
    height: 80px;
}

.nav-brand {
    display: flex;
    align-items: center;
    gap: var(--space-3);
    font-weight: 800;
    font-size: var(--font-size-xl);
    color: var(--gray-900);
}

.brand-logo {
    width: 40px;
    height: 40px;
    background: linear-gradient(135deg, var(--primary-500), var(--primary-600));
    border-radius: var(--radius-lg);
    display: flex;
    align-items: center;
    justify-content: center;
    color: var(--white);
    font-size: var(--font-size-lg);
}

.nav-menu {
    display: flex;
    align-items: center;
    gap: var(--space-8);
}

.nav-link {
    font-weight: 500;
    color: var(--gray-600);
    position: relative;
    padding: var(--space-2) 0;
    transition: var(--transition-fast);
}

.nav-link:hover,
.nav-link.active {
    color: var(--primary-600);
}

.nav-link::after {
    content: '';
    position: absolute;
    bottom: 0;
    left: 0;
    width: 0;
    height: 2px;
    background: var(--primary-500);
    transition: var(--transition-fast);
}

.nav-link:hover::after,
.nav-link.active::after {
    width: 100%;
}

.nav-actions {
    display: flex;
    align-items: center;
    gap: var(--space-4);
}

.btn-login {
    font-weight: 500;
    color: var(--gray-600);
    padding: var(--space-2) var(--space-4);
    border-radius: var(--radius-md);
    transition: var(--transition-fast);
}

.btn-login:hover {
    color: var(--primary-600);
    background: var(--primary-50);
}

.btn-primary {
    background: linear-gradient(135deg, var(--primary-500), var(--primary-600));
    color: var(--white);
    font-weight: 600;
    padding: var(--space-3) var(--space-6);
    border-radius: var(--radius-lg);
    border: none;
    cursor: pointer;
    transition: var(--transition-fast);
    box-shadow: var(--shadow-sm);
}

.btn-primary:hover {
    transform: translateY(-2px);
    box-shadow: var(--shadow-lg);
}

.mobile-toggle {
    display: none;
    flex-direction: column;
    gap: 4px;
    cursor: pointer;
    padding: var(--space-2);
}

.mobile-toggle span {
    width: 24px;
    height: 2px;
    background: var(--gray-600);
    border-radius: var(--radius-full);
    transition: var(--transition-fast);
}

/* Hero Section */
.hero {
    position: relative;
    min-height: 100vh;
    display: flex;
    align-items: center;
    padding-top: 80px;
    background: linear-gradient(135deg, var(--gray-50) 0%, var(--white) 100%);
    overflow: hidden;
}

.hero-content {
    display: grid;
    grid-template-columns: 1fr 1fr;
    gap: var(--space-16);
    align-items: center;
    position: relative;
    z-index: 2;
}

.hero-left {
    max-width: 600px;
}

.hero-badge {
    display: inline-flex;
    align-items: center;
    gap: var(--space-2);
    background: var(--primary-50);
    color: var(--primary-700);
    padding: var(--space-2) var(--space-4);
    border-radius: var(--radius-full);
    font-size: var(--font-size-sm);
    font-weight: 500;
    margin-bottom: var(--space-6);
    border: 1px solid var(--primary-100);
}

.hero-title {
    font-size: var(--font-size-6xl);
    font-weight: 800;
    line-height: 1.1;
    margin-bottom: var(--space-6);
    color: var(--gray-900);
}

.highlight {
    background: linear-gradient(135deg, var(--primary-500), var(--primary-600));
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    background-clip: text;
}

.hero-description {
    font-size: var(--font-size-xl);
    color: var(--gray-600);
    margin-bottom: var(--space-8);
    line-height: 1.7;
}

.hero-actions {
    display: flex;
    align-items: center;
    gap: var(--space-4);
    margin-bottom: var(--space-12);
}

.btn-hero-primary {
    background: linear-gradient(135deg, var(--primary-500), var(--primary-600));
    color: var(--white);
    font-weight: 600;
    padding: var(--space-4) var(--space-8);
    border-radius: var(--radius-xl);
    border: none;
    cursor: pointer;
    transition: var(--transition-fast);
    box-shadow: var(--shadow-lg);
    display: flex;
    align-items: center;
    gap: var(--space-2);
    font-size: var(--font-size-lg);
}

.btn-hero-primary:hover {
    transform: translateY(-3px);
    box-shadow: var(--shadow-xl);
}

.btn-hero-secondary {
    background: var(--white);
    color: var(--gray-700);
    font-weight: 600;
    padding: var(--space-4) var(--space-8);
    border-radius: var(--radius-xl);
    border: 2px solid var(--gray-200);
    cursor: pointer;
    transition: var(--transition-fast);
    display: flex;
    align-items: center;
    gap: var(--space-2);
    font-size: var(--font-size-lg);
}

.btn-hero-secondary:hover {
    border-color: var(--primary-300);
    color: var(--primary-600);
    transform: translateY(-2px);
    box-shadow: var(--shadow-md);
}

.hero-stats {
    display: flex;
    gap: var(--space-8);
}

.stat-item {
    text-align: center;
}

.stat-number {
    display: block;
    font-size: var(--font-size-3xl);
    font-weight: 800;
    color: var(--gray-900);
    margin-bottom: var(--space-1);
}

.stat-label {
    font-size: var(--font-size-sm);
    color: var(--gray-500);
    font-weight: 500;
}

/* Hero Visual */
.hero-right {
    display: flex;
    justify-content: center;
    align-items: center;
}

.hero-visual {
    position: relative;
    max-width: 500px;
    width: 100%;
}

.booking-card {
    background: var(--white);
    border-radius: var(--radius-2xl);
    padding: var(--space-8);
    box-shadow: var(--shadow-2xl);
    border: 1px solid var(--gray-100);
    position: relative;
    z-index: 2;
}

.booking-header {
    display: flex;
    align-items: flex-start;
    justify-content: space-between;
    margin-bottom: var(--space-6);
}

.provider-info {
    display: flex;
    gap: var(--space-4);
    flex: 1;
}

.provider-avatar {
    width: 60px;
    height: 60px;
    border-radius: var(--radius-full);
    overflow: hidden;
    flex-shrink: 0;
}

.provider-avatar img {
    width: 100%;
    height: 100%;
    object-fit: cover;
}

.provider-details h4 {
    font-size: var(--font-size-lg);
    font-weight: 600;
    color: var(--gray-900);
    margin-bottom: var(--space-1);
}

.provider-details p {
    font-size: var(--font-size-sm);
    color: var(--gray-500);
    margin-bottom: var(--space-2);
}

.rating {
    display: flex;
    align-items: center;
    gap: var(--space-1);
    font-size: var(--font-size-sm);
}

.rating i {
    color: var(--warning-500);
}

.rating span {
    color: var(--gray-600);
    font-weight: 500;
}

.booking-status {
    flex-shrink: 0;
}

.status-badge {
    padding: var(--space-1) var(--space-3);
    border-radius: var(--radius-full);
    font-size: var(--font-size-xs);
    font-weight: 600;
    text-transform: uppercase;
    letter-spacing: 0.05em;
}

.status-badge.available {
    background: rgba(16, 185, 129, 0.1);
    color: var(--success-500);
}

.booking-details {
    margin-bottom: var(--space-6);
}

.detail-item {
    display: flex;
    align-items: center;
    gap: var(--space-3);
    margin-bottom: var(--space-3);
    font-size: var(--font-size-sm);
    color: var(--gray-600);
}

.detail-item i {
    color: var(--primary-500);
    width: 16px;
    flex-shrink: 0;
}

.booking-btn {
    width: 100%;
    background: linear-gradient(135deg, var(--primary-500), var(--primary-600));
    color: var(--white);
    border: none;
    padding: var(--space-4) var(--space-6);
    border-radius: var(--radius-xl);
    font-weight: 600;
    font-size: var(--font-size-base);
    cursor: pointer;
    transition: var(--transition-fast);
    display: flex;
    align-items: center;
    justify-content: center;
    gap: var(--space-2);
}

.booking-btn:hover {
    transform: translateY(-2px);
    box-shadow: var(--shadow-lg);
}

/* Floating Elements */
.floating-elements {
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    pointer-events: none;
}

.floating-card {
    position: absolute;
    background: var(--white);
    border-radius: var(--radius-lg);
    padding: var(--space-3) var(--space-4);
    box-shadow: var(--shadow-lg);
    border: 1px solid var(--gray-100);
    display: flex;
    align-items: center;
    gap: var(--space-2);
    font-size: var(--font-size-sm);
    font-weight: 500;
    color: var(--gray-700);
    animation: float 3s ease-in-out infinite;
}

.floating-card i {
    color: var(--primary-500);
}

.card-1 {
    top: 20%;
    right: -10%;
    animation-delay: 0s;
}

.card-2 {
    bottom: 30%;
    left: -15%;
    animation-delay: 1s;
}

.card-3 {
    top: 60%;
    right: -5%;
    animation-delay: 2s;
}

@keyframes float {
    0%, 100% { transform: translateY(0px); }
    50% { transform: translateY(-10px); }
}

/* Background Shapes */
.hero-background {
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    z-index: 1;
    overflow: hidden;
}

.bg-shape {
    position: absolute;
    border-radius: 50%;
    background: linear-gradient(135deg, var(--primary-100), var(--primary-50));
    opacity: 0.6;
    animation: float 8s ease-in-out infinite;
}

.shape-1 {
    width: 400px;
    height: 400px;
    top: -200px;
    right: -200px;
    animation-delay: 0s;
}

.shape-2 {
    width: 300px;
    height: 300px;
    bottom: -150px;
    left: -150px;
    animation-delay: 2s;
}

.shape-3 {
    width: 200px;
    height: 200px;
    top: 50%;
    left: 10%;
    animation-delay: 4s;
}

/* Features Section */
.features {
    padding: var(--space-24) 0;
    background: var(--gray-50);
}

.section-header {
    text-align: center;
    margin-bottom: var(--space-16);
    max-width: 800px;
    margin-left: auto;
    margin-right: auto;
}

.section-badge {
    display: inline-flex;
    align-items: center;
    gap: var(--space-2);
    background: var(--primary-50);
    color: var(--primary-700);
    padding: var(--space-2) var(--space-4);
    border-radius: var(--radius-full);
    font-size: var(--font-size-sm);
    font-weight: 500;
    margin-bottom: var(--space-4);
    border: 1px solid var(--primary-100);
}

.section-title {
    font-size: var(--font-size-5xl);
    font-weight: 800;
    color: var(--gray-900);
    margin-bottom: var(--space-4);
    line-height: 1.1;
}

.section-description {
    font-size: var(--font-size-xl);
    color: var(--gray-600);
    line-height: 1.7;
}

.features-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(350px, 1fr));
    gap: var(--space-8);
}

.feature-card {
    background: var(--white);
    border-radius: var(--radius-2xl);
    padding: var(--space-8);
    text-align: center;
    box-shadow: var(--shadow-sm);
    border: 1px solid var(--gray-100);
    transition: var(--transition-normal);
    position: relative;
    overflow: hidden;
}

.feature-card::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    height: 4px;
    background: linear-gradient(135deg, var(--primary-500), var(--primary-600));
    transform: scaleX(0);
    transition: var(--transition-normal);
}

.feature-card:hover::before {
    transform: scaleX(1);
}

.feature-card:hover {
    transform: translateY(-8px);
    box-shadow: var(--shadow-xl);
}

.feature-icon {
    width: 80px;
    height: 80px;
    background: linear-gradient(135deg, var(--primary-500), var(--primary-600));
    border-radius: var(--radius-2xl);
    display: flex;
    align-items: center;
    justify-content: center;
    margin: 0 auto var(--space-6);
    font-size: var(--font-size-3xl);
    color: var(--white);
    position: relative;
}

.feature-icon::after {
    content: '';
    position: absolute;
    top: -4px;
    left: -4px;
    right: -4px;
    bottom: -4px;
    background: linear-gradient(135deg, var(--primary-500), var(--primary-600));
    border-radius: var(--radius-2xl);
    z-index: -1;
    opacity: 0.2;
    transform: scale(0.8);
    transition: var(--transition-normal);
}

.feature-card:hover .feature-icon::after {
    transform: scale(1);
    opacity: 0.3;
}

.feature-card h3 {
    font-size: var(--font-size-2xl);
    font-weight: 700;
    color: var(--gray-900);
    margin-bottom: var(--space-4);
}

.feature-card p {
    color: var(--gray-600);
    line-height: 1.7;
    font-size: var(--font-size-base);
}

/* Services Section */
.services {
    padding: var(--space-24) 0;
    background: var(--white);
}

.services-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
    gap: var(--space-8);
}

.service-card {
    background: var(--white);
    border-radius: var(--radius-2xl);
    padding: var(--space-8);
    text-align: center;
    box-shadow: var(--shadow-sm);
    border: 1px solid var(--gray-100);
    transition: var(--transition-normal);
    position: relative;
    overflow: hidden;
}

.service-card::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: linear-gradient(135deg, var(--primary-50), var(--primary-100));
    opacity: 0;
    transition: var(--transition-normal);
}

.service-card:hover::before {
    opacity: 1;
}

.service-card:hover {
    transform: translateY(-8px);
    box-shadow: var(--shadow-xl);
    border-color: var(--primary-200);
}

.service-icon {
    width: 80px;
    height: 80px;
    background: linear-gradient(135deg, var(--primary-500), var(--primary-600));
    border-radius: var(--radius-2xl);
    display: flex;
    align-items: center;
    justify-content: center;
    margin: 0 auto var(--space-6);
    font-size: var(--font-size-3xl);
    color: var(--white);
    position: relative;
    z-index: 2;
}

.service-card h3 {
    font-size: var(--font-size-2xl);
    font-weight: 700;
    color: var(--gray-900);
    margin-bottom: var(--space-4);
    position: relative;
    z-index: 2;
}

.service-card p {
    color: var(--gray-600);
    line-height: 1.7;
    margin-bottom: var(--space-6);
    position: relative;
    z-index: 2;
}

.service-meta {
    display: flex;
    justify-content: space-between;
    align-items: center;
    font-size: var(--font-size-sm);
    position: relative;
    z-index: 2;
}

.service-count {
    color: var(--gray-500);
    font-weight: 500;
}

.service-rating {
    display: flex;
    align-items: center;
    gap: var(--space-1);
    color: var(--gray-700);
    font-weight: 600;
}

.service-rating i {
    color: var(--warning-500);
}

/* CTA Section */
.cta {
    padding: var(--space-24) 0;
    background: linear-gradient(135deg, var(--primary-500), var(--primary-600));
    text-align: center;
    position: relative;
    overflow: hidden;
}

.cta::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: url("data:image/svg+xml,%3Csvg width='60' height='60' viewBox='0 0 60 60' xmlns='http://www.w3.org/2000/svg'%3E%3Cg fill='none' fill-rule='evenodd'%3E%3Cg fill='%23ffffff' fill-opacity='0.05'%3E%3Ccircle cx='30' cy='30' r='2'/%3E%3C/g%3E%3C/g%3E%3C/svg%3E");
    opacity: 0.5;
}

.cta-content {
    position: relative;
    z-index: 2;
    max-width: 800px;
    margin: 0 auto;
}

.cta-content h2 {
    font-size: var(--font-size-5xl);
    font-weight: 800;
    color: var(--white);
    margin-bottom: var(--space-4);
}

.cta-content p {
    font-size: var(--font-size-xl);
    color: rgba(255, 255, 255, 0.9);
    margin-bottom: var(--space-8);
    line-height: 1.7;
}

.cta-actions {
    display: flex;
    gap: var(--space-4);
    justify-content: center;
    flex-wrap: wrap;
}

.btn-cta-primary {
    background: var(--white);
    color: var(--primary-600);
    font-weight: 600;
    padding: var(--space-4) var(--space-8);
    border-radius: var(--radius-xl);
    border: none;
    cursor: pointer;
    transition: var(--transition-fast);
    display: flex;
    align-items: center;
    gap: var(--space-2);
    font-size: var(--font-size-lg);
    box-shadow: var(--shadow-lg);
}

.btn-cta-primary:hover {
    transform: translateY(-3px);
    box-shadow: var(--shadow-xl);
}

.btn-cta-secondary {
    background: transparent;
    color: var(--white);
    font-weight: 600;
    padding: var(--space-4) var(--space-8);
    border-radius: var(--radius-xl);
    border: 2px solid rgba(255, 255, 255, 0.3);
    cursor: pointer;
    transition: var(--transition-fast);
    display: flex;
    align-items: center;
    gap: var(--space-2);
    font-size: var(--font-size-lg);
}

.btn-cta-secondary:hover {
    background: rgba(255, 255, 255, 0.1);
    border-color: var(--white);
    transform: translateY(-2px);
}

/* Footer */
.footer {
    background: var(--gray-900);
    color: var(--white);
    padding: var(--space-20) 0 var(--space-8);
}

.footer-content {
    display: grid;
    grid-template-columns: 1fr 3fr;
    gap: var(--space-16);
    margin-bottom: var(--space-12);
}

.footer-brand .brand {
    display: flex;
    align-items: center;
    gap: var(--space-3);
    font-size: var(--font-size-xl);
    font-weight: 800;
    margin-bottom: var(--space-4);
}

.footer-brand .brand-logo {
    background: linear-gradient(135deg, var(--primary-500), var(--primary-600));
}

.footer-brand p {
    color: var(--gray-400);
    line-height: 1.7;
    margin-bottom: var(--space-6);
}

.footer-social {
    display: flex;
    gap: var(--space-3);
}

.social-link {
    width: 44px;
    height: 44px;
    background: var(--gray-800);
    border-radius: var(--radius-lg);
    display: flex;
    align-items: center;
    justify-content: center;
    color: var(--gray-400);
    transition: var(--transition-fast);
    font-size: var(--font-size-lg);
}

.social-link:hover {
    background: var(--primary-600);
    color: var(--white);
    transform: translateY(-2px);
}

.footer-links {
    display: grid;
    grid-template-columns: repeat(4, 1fr);
    gap: var(--space-8);
}

.footer-column h4 {
    font-size: var(--font-size-lg);
    font-weight: 600;
    margin-bottom: var(--space-4);
    color: var(--white);
}

.footer-column a {
    display: block;
    color: var(--gray-400);
    margin-bottom: var(--space-2);
    transition: var(--transition-fast);
    font-size: var(--font-size-sm);
}

.footer-column a:hover {
    color: var(--primary-400);
    transform: translateX(4px);
}

.footer-bottom {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding-top: var(--space-8);
    border-top: 1px solid var(--gray-800);
}

.footer-bottom p {
    color: var(--gray-400);
    font-size: var(--font-size-sm);
}

.footer-meta {
    color: var(--gray-500);
    font-size: var(--font-size-sm);
}

/* Auth Pages */
.auth-wrapper {
    background: var(--white);
    border-radius: var(--radius-2xl);
    padding: var(--space-12);
    box-shadow: var(--shadow-xl);
    border: 1px solid var(--gray-100);
    max-width: 500px;
    margin: var(--space-20) auto;
}

.auth-header {
    text-align: center;
    margin-bottom: var(--space-10);
}

.auth-header h1 {
    font-size: var(--font-size-3xl);
    font-weight: 800;
    color: var(--gray-900);
    margin-bottom: var(--space-2);
}

.auth-header h1 i {
    color: var(--primary-500);
    margin-right: var(--space-3);
}

.auth-header p {
    color: var(--gray-600);
    font-size: var(--font-size-lg);
}

.auth-form {
    display: flex;
    flex-direction: column;
    gap: var(--space-6);
}

.auth-links {
    text-align: center;
    margin-top: var(--space-8);
    padding-top: var(--space-6);
    border-top: 1px solid var(--gray-200);
}

.auth-links p {
    margin-bottom: var(--space-2);
    color: var(--gray-600);
}

.auth-links a {
    color: var(--primary-600);
    font-weight: 500;
}

.auth-links a:hover {
    color: var(--primary-700);
    text-decoration: underline;
}

/* Form Styles */
.form-group {
    position: relative;
}

.form-group label {
    display: block;
    margin-bottom: var(--space-2);
    color: var(--gray-700);
    font-weight: 500;
    font-size: var(--font-size-sm);
}

.form-group label i {
    color: var(--primary-500);
    margin-right: var(--space-2);
    width: 16px;
}

.form-group input,
.form-group select,
.form-group textarea {
    width: 100%;
    padding: var(--space-4) var(--space-5);
    border: 2px solid var(--gray-200);
    border-radius: var(--radius-lg);
    font-size: var(--font-size-base);
    font-family: inherit;
    background: var(--white);
    transition: var(--transition-fast);
    outline: none;
}

.form-group input:focus,
.form-group select:focus,
.form-group textarea:focus {
    border-color: var(--primary-500);
    box-shadow: 0 0 0 3px rgba(59, 130, 246, 0.1);
}

.form-group input.error,
.form-group select.error,
.form-group textarea.error {
    border-color: var(--error-500);
    box-shadow: 0 0 0 3px rgba(239, 68, 68, 0.1);
}

.form-group select {
    cursor: pointer;
    appearance: none;
    background-image: url("data:image/svg+xml,%3csvg xmlns='http://www.w3.org/2000/svg' fill='none' viewBox='0 0 20 20'%3e%3cpath stroke='%236b7280' stroke-linecap='round' stroke-linejoin='round' stroke-width='1.5' d='m6 8 4 4 4-4'/%3e%3c/svg%3e");
    background-position: right var(--space-3) center;
    background-repeat: no-repeat;
    background-size: 16px;
    padding-right: var(--space-10);
}

.form-group textarea {
    resize: vertical;
    min-height: 120px;
}

.form-row {
    display: grid;
    grid-template-columns: 1fr 1fr;
    gap: var(--space-5);
}

.error-message {
    display: block;
    color: var(--error-500);
    font-size: var(--font-size-sm);
    margin-top: var(--space-1);
    min-height: 20px;
}

.help-text {
    display: block;
    color: var(--gray-500);
    font-size: var(--font-size-sm);
    margin-top: var(--space-1);
}

.submit-btn {
    background: linear-gradient(135deg, var(--primary-500), var(--primary-600));
    color: var(--white);
    border: none;
    padding: var(--space-4) var(--space-8);
    border-radius: var(--radius-lg);
    font-size: var(--font-size-lg);
    font-weight: 600;
    cursor: pointer;
    transition: var(--transition-fast);
    position: relative;
    overflow: hidden;
    width: 100%;
}

.submit-btn:hover:not(:disabled) {
    transform: translateY(-2px);
    box-shadow: var(--shadow-lg);
}

.submit-btn:disabled {
    opacity: 0.7;
    cursor: not-allowed;
    transform: none;
}

.btn-text {
    display: inline-block;
}

.btn-loader {
    display: none;
}

/* Message Styles */
.message {
    display: none;
    padding: var(--space-5);
    border-radius: var(--radius-lg);
    margin-top: var(--space-5);
    text-align: center;
}

.success-message {
    background: linear-gradient(135deg, var(--success-500), #059669);
    color: var(--white);
}

.success-message i {
    font-size: var(--font-size-3xl);
    margin-bottom: var(--space-2);
    display: block;
}

.success-message h3 {
    font-size: var(--font-size-xl);
    margin-bottom: var(--space-2);
}

.error-message-box {
    background: linear-gradient(135deg, var(--error-500), #dc2626);
    color: var(--white);
}

.error-message-box i {
    font-size: var(--font-size-3xl);
    margin-bottom: var(--space-2);
    display: block;
}

.error-message-box h3 {
    font-size: var(--font-size-xl);
    margin-bottom: var(--space-2);
}

/* Responsive Design */
@media (max-width: 1024px) {
    .hero-content {
        grid-template-columns: 1fr;
        gap: var(--space-12);
        text-align: center;
    }

    .hero-title {
        font-size: var(--font-size-5xl);
    }

    .features-grid {
        grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
    }

    .services-grid {
        grid-template-columns: repeat(auto-fit, minmax(280px, 1fr));
    }

    .footer-content {
        grid-template-columns: 1fr;
        gap: var(--space-12);
    }

    .footer-links {
        grid-template-columns: repeat(2, 1fr);
    }
}

@media (max-width: 768px) {
    .nav-menu {
        display: none;
    }

    .mobile-toggle {
        display: flex;
    }

    .nav-actions {
        gap: var(--space-2);
    }

    .btn-primary {
        padding: var(--space-2) var(--space-4);
        font-size: var(--font-size-sm);
    }

    .hero-title {
        font-size: var(--font-size-4xl);
    }

    .hero-description {
        font-size: var(--font-size-lg);
    }

    .hero-actions {
        flex-direction: column;
        align-items: center;
    }

    .hero-stats {
        justify-content: center;
        gap: var(--space-6);
    }

    .section-title {
        font-size: var(--font-size-4xl);
    }

    .features-grid,
    .services-grid {
        grid-template-columns: 1fr;
    }

    .footer-links {
        grid-template-columns: 1fr;
        gap: var(--space-6);
    }

    .footer-bottom {
        flex-direction: column;
        gap: var(--space-4);
        text-align: center;
    }

    .form-row {
        grid-template-columns: 1fr;
    }

    .cta-actions {
        flex-direction: column;
        align-items: center;
    }

    .auth-wrapper {
        margin: var(--space-8) auto;
        padding: var(--space-8);
    }
}

@media (max-width: 480px) {
    .container {
        padding: 0 var(--space-4);
    }

    .hero-title {
        font-size: var(--font-size-3xl);
    }

    .hero-description {
        font-size: var(--font-size-base);
    }

    .btn-hero-primary,
    .btn-hero-secondary {
        padding: var(--space-3) var(--space-6);
        font-size: var(--font-size-base);
    }

    .section-title {
        font-size: var(--font-size-3xl);
    }

    .feature-card,
    .service-card {
        padding: var(--space-6);
    }

    .booking-card {
        padding: var(--space-6);
    }

    .floating-card {
        display: none;
    }

    .auth-wrapper {
        margin: var(--space-4) auto;
        padding: var(--space-6);
    }

    .auth-header h1 {
        font-size: var(--font-size-2xl);
    }
}
