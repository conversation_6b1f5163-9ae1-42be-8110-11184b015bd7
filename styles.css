/* Reset and base styles */
* {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
}

body {
    font-family: 'Inter', sans-serif;
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    min-height: 100vh;
    display: flex;
    align-items: center;
    justify-content: center;
    padding: 20px;
}

.container {
    width: 100%;
    max-width: 600px;
}

.form-wrapper {
    background: rgba(255, 255, 255, 0.95);
    backdrop-filter: blur(10px);
    border-radius: 20px;
    padding: 40px;
    box-shadow: 0 20px 40px rgba(0, 0, 0, 0.1);
    border: 1px solid rgba(255, 255, 255, 0.2);
}

.form-header {
    text-align: center;
    margin-bottom: 40px;
}

.form-header h1 {
    color: #2d3748;
    font-size: 2.5rem;
    font-weight: 700;
    margin-bottom: 10px;
}

.form-header h1 i {
    color: #667eea;
    margin-right: 15px;
}

.form-header p {
    color: #718096;
    font-size: 1.1rem;
    line-height: 1.6;
}

.admin-link {
    margin-top: 20px;
}

.admin-btn {
    display: inline-flex;
    align-items: center;
    gap: 8px;
    padding: 10px 20px;
    background: linear-gradient(135deg, #48bb78 0%, #38a169 100%);
    color: white;
    text-decoration: none;
    border-radius: 8px;
    font-size: 0.9rem;
    font-weight: 500;
    transition: all 0.3s ease;
}

.admin-btn:hover {
    transform: translateY(-2px);
    box-shadow: 0 8px 25px rgba(72, 187, 120, 0.3);
}

.contact-form {
    display: flex;
    flex-direction: column;
    gap: 25px;
}

.form-group {
    position: relative;
}

.form-group label {
    display: block;
    margin-bottom: 8px;
    color: #2d3748;
    font-weight: 500;
    font-size: 0.95rem;
}

.form-group label i {
    color: #667eea;
    margin-right: 8px;
    width: 16px;
}

.form-group input,
.form-group select,
.form-group textarea {
    width: 100%;
    padding: 15px 20px;
    border: 2px solid #e2e8f0;
    border-radius: 12px;
    font-size: 1rem;
    font-family: inherit;
    transition: all 0.3s ease;
    background: #fff;
}

.form-group input:focus,
.form-group select:focus,
.form-group textarea:focus {
    outline: none;
    border-color: #667eea;
    box-shadow: 0 0 0 3px rgba(102, 126, 234, 0.1);
    transform: translateY(-2px);
}

.form-group input:hover,
.form-group select:hover,
.form-group textarea:hover {
    border-color: #cbd5e0;
}

.form-group textarea {
    resize: vertical;
    min-height: 120px;
}

.form-group select {
    cursor: pointer;
}

/* Checkbox styling */
.checkbox-group {
    margin: 10px 0;
}

.checkbox-label {
    display: flex;
    align-items: center;
    cursor: pointer;
    font-size: 0.95rem;
    color: #4a5568;
    line-height: 1.5;
}

.checkbox-label input[type="checkbox"] {
    display: none;
}

.checkmark {
    width: 20px;
    height: 20px;
    border: 2px solid #e2e8f0;
    border-radius: 4px;
    margin-right: 12px;
    position: relative;
    transition: all 0.3s ease;
    flex-shrink: 0;
}

.checkbox-label input[type="checkbox"]:checked + .checkmark {
    background: #667eea;
    border-color: #667eea;
}

.checkbox-label input[type="checkbox"]:checked + .checkmark::after {
    content: '✓';
    position: absolute;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%);
    color: white;
    font-size: 12px;
    font-weight: bold;
}

/* Submit button */
.submit-btn {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    color: white;
    border: none;
    padding: 18px 40px;
    border-radius: 12px;
    font-size: 1.1rem;
    font-weight: 600;
    cursor: pointer;
    transition: all 0.3s ease;
    position: relative;
    overflow: hidden;
    margin-top: 10px;
}

.submit-btn:hover {
    transform: translateY(-2px);
    box-shadow: 0 10px 25px rgba(102, 126, 234, 0.3);
}

.submit-btn:active {
    transform: translateY(0);
}

.submit-btn.loading {
    pointer-events: none;
}

.btn-text,
.btn-loader {
    transition: opacity 0.3s ease;
}

.btn-loader {
    position: absolute;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%);
    opacity: 0;
}

.submit-btn.loading .btn-text {
    opacity: 0;
}

.submit-btn.loading .btn-loader {
    opacity: 1;
}

/* Error messages */
.error-message {
    color: #e53e3e;
    font-size: 0.85rem;
    margin-top: 5px;
    display: block;
    opacity: 0;
    transition: opacity 0.3s ease;
}

.form-group.error input,
.form-group.error select,
.form-group.error textarea {
    border-color: #e53e3e;
    box-shadow: 0 0 0 3px rgba(229, 62, 62, 0.1);
}

.form-group.error .error-message {
    opacity: 1;
}

/* Success and error messages */
.message {
    display: none;
    text-align: center;
    padding: 30px;
    border-radius: 12px;
    margin-top: 20px;
}

.success-message {
    background: linear-gradient(135deg, #48bb78 0%, #38a169 100%);
    color: white;
}

.error-message-box {
    background: linear-gradient(135deg, #e53e3e 0%, #c53030 100%);
    color: white;
}

.message i {
    font-size: 3rem;
    margin-bottom: 15px;
    display: block;
}

.message h3 {
    font-size: 1.5rem;
    margin-bottom: 10px;
}

.message p {
    font-size: 1rem;
    opacity: 0.9;
}

/* Responsive design */
@media (max-width: 768px) {
    .form-wrapper {
        padding: 30px 20px;
        margin: 10px;
    }
    
    .form-header h1 {
        font-size: 2rem;
    }
    
    .form-header p {
        font-size: 1rem;
    }
    
    .form-group input,
    .form-group select,
    .form-group textarea {
        padding: 12px 16px;
    }
    
    .submit-btn {
        padding: 16px 32px;
        font-size: 1rem;
    }
}

@media (max-width: 480px) {
    .form-wrapper {
        padding: 25px 15px;
    }
    
    .form-header h1 {
        font-size: 1.8rem;
    }
    
    .contact-form {
        gap: 20px;
    }
}

/* Animation for form appearance */
.form-wrapper {
    animation: slideUp 0.6s ease-out;
}

@keyframes slideUp {
    from {
        opacity: 0;
        transform: translateY(30px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}
