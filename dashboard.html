<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Dashboard - Service Booking</title>
    <link rel="stylesheet" href="styles.css">
    <link href="https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700&display=swap" rel="stylesheet">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css">
</head>
<body>
    <div class="container">
        <div class="dashboard-wrapper">
            <div class="dashboard-header">
                <div class="header-content">
                    <h1><i class="fas fa-tachometer-alt"></i> Dashboard</h1>
                    <p>Welcome back! Manage your service bookings below.</p>
                </div>
                <div class="header-actions">
                    <span id="userEmail" class="user-info"></span>
                    <button id="logoutBtn" class="btn btn-outline">
                        <i class="fas fa-sign-out-alt"></i>
                        Logout
                    </button>
                </div>
            </div>

            <div class="dashboard-content">
                <!-- Booking Form Section -->
                <div class="booking-section">
                    <div class="section-header">
                        <h2><i class="fas fa-plus-circle"></i> Book a Service</h2>
                        <p>Fill out the form below to schedule your appointment</p>
                    </div>
                    
                    <form id="bookingForm" class="booking-form">
                        <div class="form-row">
                            <div class="form-group">
                                <label for="serviceType">
                                    <i class="fas fa-cogs"></i>
                                    Service Type
                                </label>
                                <select id="serviceType" name="serviceType" required>
                                    <option value="">Select a service</option>
                                    <option value="consultation">Consultation</option>
                                    <option value="maintenance">Maintenance</option>
                                    <option value="repair">Repair</option>
                                    <option value="installation">Installation</option>
                                    <option value="training">Training</option>
                                    <option value="support">Technical Support</option>
                                </select>
                                <span class="error-message"></span>
                            </div>
                        </div>

                        <div class="form-row">
                            <div class="form-group">
                                <label for="bookingDate">
                                    <i class="fas fa-calendar"></i>
                                    Date
                                </label>
                                <input type="date" id="bookingDate" name="bookingDate" required>
                                <span class="error-message"></span>
                            </div>

                            <div class="form-group">
                                <label for="bookingTime">
                                    <i class="fas fa-clock"></i>
                                    Time
                                </label>
                                <input type="time" id="bookingTime" name="bookingTime" required>
                                <span class="error-message"></span>
                            </div>
                        </div>

                        <div class="form-group">
                            <label for="notes">
                                <i class="fas fa-sticky-note"></i>
                                Notes (Optional)
                            </label>
                            <textarea id="notes" name="notes" rows="3" placeholder="Any additional information or special requirements..."></textarea>
                            <span class="error-message"></span>
                        </div>

                        <div class="form-group">
                            <button type="submit" class="submit-btn">
                                <span class="btn-text">
                                    <i class="fas fa-calendar-plus"></i>
                                    Book Now
                                </span>
                                <span class="btn-loader">
                                    <i class="fas fa-spinner fa-spin"></i>
                                </span>
                            </button>
                        </div>
                    </form>
                </div>

                <!-- Bookings List Section -->
                <div class="bookings-section">
                    <div class="section-header">
                        <h2><i class="fas fa-list"></i> Your Bookings</h2>
                        <p>View and manage your scheduled appointments</p>
                    </div>
                    
                    <div id="bookingsContainer" class="bookings-container">
                        <div id="loadingBookings" class="loading-state">
                            <i class="fas fa-spinner fa-spin"></i>
                            <p>Loading your bookings...</p>
                        </div>
                        
                        <div id="noBookings" class="empty-state" style="display: none;">
                            <i class="fas fa-calendar-times"></i>
                            <h3>No bookings yet</h3>
                            <p>Your scheduled appointments will appear here once you book a service.</p>
                        </div>
                        
                        <div id="bookingsList" class="bookings-list"></div>
                    </div>
                </div>
            </div>

            <!-- Success/Error Messages -->
            <div id="successMessage" class="message success-message">
                <i class="fas fa-check-circle"></i>
                <h3>Booking Created Successfully!</h3>
                <p>Your appointment has been scheduled. You'll receive a confirmation email shortly.</p>
            </div>

            <div id="errorMessage" class="message error-message-box">
                <i class="fas fa-exclamation-circle"></i>
                <h3>Booking Failed</h3>
                <p id="errorText">Please try again.</p>
            </div>
        </div>
    </div>

    <script src="https://cdn.jsdelivr.net/npm/@supabase/supabase-js@2"></script>
    <script src="config.js"></script>
    <script src="auth.js"></script>
    <script src="booking.js"></script>
</body>
</html>
