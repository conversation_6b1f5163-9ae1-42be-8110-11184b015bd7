// ServicePro - Comprehensive API Integration Manager
class APIManager {
    constructor() {
        this.baseURL = window.SUPABASE_CONFIG?.url || '';
        this.apiKey = window.SUPABASE_CONFIG?.anonKey || '';
        this.cache = new Map();
        this.requestQueue = new Map();
        this.rateLimiter = new Map();
        this.retryAttempts = 3;
        this.retryDelay = 1000;
        
        this.init();
    }

    init() {
        // Set up default headers
        this.defaultHeaders = {
            'Content-Type': 'application/json',
            'apikey': this.apiKey,
            'Authorization': `Bearer ${this.apiKey}`
        };

        // Set up interceptors
        this.setupInterceptors();
        
        console.log('✅ API Manager initialized');
    }

    setupInterceptors() {
        // Request interceptor
        this.requestInterceptor = (config) => {
            // Add auth token if user is logged in
            const authManager = window.authManager;
            if (authManager?.currentUser) {
                const token = authManager.supabase.auth.session()?.access_token;
                if (token) {
                    config.headers['Authorization'] = `Bearer ${token}`;
                }
            }
            
            // Add timestamp
            config.timestamp = Date.now();
            
            return config;
        };

        // Response interceptor
        this.responseInterceptor = (response, config) => {
            // Log response time
            const responseTime = Date.now() - config.timestamp;
            console.log(`API Response: ${config.method} ${config.url} - ${responseTime}ms`);
            
            return response;
        };
    }

    // Core HTTP Methods
    async request(config) {
        const {
            method = 'GET',
            url,
            data = null,
            headers = {},
            cache = false,
            cacheTime = 300000, // 5 minutes
            retry = true,
            timeout = 10000
        } = config;

        // Apply request interceptor
        const finalConfig = this.requestInterceptor({
            method,
            url,
            data,
            headers: { ...this.defaultHeaders, ...headers },
            cache,
            cacheTime,
            retry,
            timeout
        });

        // Check cache first
        if (cache && method === 'GET') {
            const cached = this.getFromCache(url);
            if (cached) {
                console.log('📦 Cache hit:', url);
                return cached;
            }
        }

        // Check rate limiting
        if (this.isRateLimited(url)) {
            throw new Error('Rate limit exceeded. Please try again later.');
        }

        // Make request with retry logic
        let lastError;
        for (let attempt = 1; attempt <= (retry ? this.retryAttempts : 1); attempt++) {
            try {
                const response = await this.makeRequest(finalConfig);
                
                // Apply response interceptor
                const finalResponse = this.responseInterceptor(response, finalConfig);
                
                // Cache successful GET requests
                if (cache && method === 'GET' && response.ok) {
                    this.setCache(url, finalResponse, cacheTime);
                }
                
                return finalResponse;
                
            } catch (error) {
                lastError = error;
                
                if (attempt < this.retryAttempts && this.shouldRetry(error)) {
                    console.warn(`🔄 Retry attempt ${attempt} for ${url}`);
                    await this.delay(this.retryDelay * attempt);
                    continue;
                }
                
                break;
            }
        }
        
        throw lastError;
    }

    async makeRequest(config) {
        const { method, url, data, headers, timeout } = config;
        
        const controller = new AbortController();
        const timeoutId = setTimeout(() => controller.abort(), timeout);
        
        try {
            const response = await fetch(url, {
                method,
                headers,
                body: data ? JSON.stringify(data) : null,
                signal: controller.signal
            });
            
            clearTimeout(timeoutId);
            
            if (!response.ok) {
                throw new Error(`HTTP ${response.status}: ${response.statusText}`);
            }
            
            const responseData = await response.json();
            return {
                ok: true,
                status: response.status,
                data: responseData,
                headers: response.headers
            };
            
        } catch (error) {
            clearTimeout(timeoutId);
            throw error;
        }
    }

    // HTTP Method Shortcuts
    async get(url, options = {}) {
        return this.request({ method: 'GET', url, ...options });
    }

    async post(url, data, options = {}) {
        return this.request({ method: 'POST', url, data, ...options });
    }

    async put(url, data, options = {}) {
        return this.request({ method: 'PUT', url, data, ...options });
    }

    async patch(url, data, options = {}) {
        return this.request({ method: 'PATCH', url, data, ...options });
    }

    async delete(url, options = {}) {
        return this.request({ method: 'DELETE', url, ...options });
    }

    // Supabase-specific methods
    async supabaseQuery(table, options = {}) {
        const {
            select = '*',
            filters = {},
            orderBy = null,
            limit = null,
            offset = null,
            cache = true
        } = options;

        let url = `${this.baseURL}/rest/v1/${table}?select=${select}`;
        
        // Add filters
        Object.entries(filters).forEach(([key, value]) => {
            if (Array.isArray(value)) {
                url += `&${key}=in.(${value.join(',')})`;
            } else {
                url += `&${key}=eq.${value}`;
            }
        });
        
        // Add ordering
        if (orderBy) {
            url += `&order=${orderBy}`;
        }
        
        // Add limit
        if (limit) {
            url += `&limit=${limit}`;
        }
        
        // Add offset
        if (offset) {
            url += `&offset=${offset}`;
        }

        return this.get(url, { cache });
    }

    async supabaseInsert(table, data, options = {}) {
        const url = `${this.baseURL}/rest/v1/${table}`;
        const headers = {
            'Prefer': 'return=representation'
        };
        
        return this.post(url, data, { headers, ...options });
    }

    async supabaseUpdate(table, id, data, options = {}) {
        const url = `${this.baseURL}/rest/v1/${table}?id=eq.${id}`;
        const headers = {
            'Prefer': 'return=representation'
        };
        
        return this.patch(url, data, { headers, ...options });
    }

    async supabaseDelete(table, id, options = {}) {
        const url = `${this.baseURL}/rest/v1/${table}?id=eq.${id}`;
        return this.delete(url, options);
    }

    // Cache Management
    getFromCache(key) {
        const cached = this.cache.get(key);
        if (cached && Date.now() < cached.expiry) {
            return cached.data;
        }
        this.cache.delete(key);
        return null;
    }

    setCache(key, data, ttl = 300000) {
        this.cache.set(key, {
            data,
            expiry: Date.now() + ttl
        });
    }

    clearCache(pattern = null) {
        if (pattern) {
            for (const key of this.cache.keys()) {
                if (key.includes(pattern)) {
                    this.cache.delete(key);
                }
            }
        } else {
            this.cache.clear();
        }
    }

    // Rate Limiting
    isRateLimited(url) {
        const key = this.getRateLimitKey(url);
        const limit = this.rateLimiter.get(key);
        
        if (!limit) return false;
        
        return Date.now() < limit.resetTime && limit.count >= limit.maxRequests;
    }

    updateRateLimit(url, maxRequests = 100, windowMs = 60000) {
        const key = this.getRateLimitKey(url);
        const now = Date.now();
        const limit = this.rateLimiter.get(key);
        
        if (!limit || now >= limit.resetTime) {
            this.rateLimiter.set(key, {
                count: 1,
                maxRequests,
                resetTime: now + windowMs
            });
        } else {
            limit.count++;
        }
    }

    getRateLimitKey(url) {
        return new URL(url).hostname;
    }

    // Utility Methods
    shouldRetry(error) {
        // Retry on network errors or 5xx status codes
        return error.name === 'TypeError' || 
               (error.message.includes('HTTP 5')) ||
               error.name === 'AbortError';
    }

    delay(ms) {
        return new Promise(resolve => setTimeout(resolve, ms));
    }

    // Batch Requests
    async batch(requests) {
        const promises = requests.map(request => this.request(request));
        return Promise.allSettled(promises);
    }

    // Health Check
    async healthCheck() {
        try {
            const response = await this.get(`${this.baseURL}/rest/v1/`, {
                timeout: 5000,
                cache: false
            });
            return { status: 'healthy', response };
        } catch (error) {
            return { status: 'unhealthy', error: error.message };
        }
    }
}

// Initialize global API manager
window.apiManager = new APIManager();

// Export for module use
if (typeof module !== 'undefined' && module.exports) {
    module.exports = APIManager;
}
