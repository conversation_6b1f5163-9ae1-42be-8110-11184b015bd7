// Supabase Configuration Template
// Copy this file to config.js and fill in your actual Supabase credentials

const SUPABASE_CONFIG = {
    url: 'https://pabikxyftoymhvzdqbhz.supabase.co',
    anon<PERSON>ey: 'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6InBhYmlreHlmdG95bWh2emRxYmh6Iiwicm9sZSI6ImFub24iLCJpYXQiOjE3NTE5Njk5MzUsImV4cCI6MjA2NzU0NTkzNX0.Wl8w02Q5XfDEqRvoIE4uobXXUXo3NJhCY8ZcjyYs1Xg'
};

// Export for use in script.js
window.SUPABASE_CONFIG = SUPABASE_CONFIG;

/* 
To get your Supabase credentials:

1. Go to https://supabase.com and sign in
2. Create a new project or select an existing one
3. Go to Settings > API
4. Copy the Project URL and anon/public key
5. Replace the values above with your actual credentials
6. Rename this file to config.js

Example:
const SUPABASE_CONFIG = {
    url: 'https://abcdefghijklmnop.supabase.co',
    anon<PERSON><PERSON>: 'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9...'
};
*/
