<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Register - Service Booking</title>
    <link rel="stylesheet" href="styles.css">
    <link href="https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700&display=swap" rel="stylesheet">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css">
</head>
<body>
    <div class="container">
        <div class="auth-wrapper">
            <div class="auth-header">
                <h1><i class="fas fa-user-plus"></i> Create Account</h1>
                <p>Join us to start booking your services</p>
            </div>
            
            <form id="registerForm" class="auth-form">
                <div class="form-group">
                    <label for="email">
                        <i class="fas fa-envelope"></i>
                        Email Address
                    </label>
                    <input type="email" id="email" name="email" required>
                    <span class="error-message"></span>
                </div>

                <div class="form-group">
                    <label for="password">
                        <i class="fas fa-lock"></i>
                        Password
                    </label>
                    <input type="password" id="password" name="password" required minlength="6">
                    <span class="error-message"></span>
                    <small class="help-text">Password must be at least 6 characters long</small>
                </div>

                <div class="form-group">
                    <label for="confirmPassword">
                        <i class="fas fa-lock"></i>
                        Confirm Password
                    </label>
                    <input type="password" id="confirmPassword" name="confirmPassword" required>
                    <span class="error-message"></span>
                </div>

                <div class="form-group">
                    <button type="submit" class="submit-btn">
                        <span class="btn-text">Create Account</span>
                        <span class="btn-loader">
                            <i class="fas fa-spinner fa-spin"></i>
                        </span>
                    </button>
                </div>
            </form>

            <div class="auth-links">
                <p>Already have an account? <a href="login.html">Login here</a></p>
                <p><a href="index.html">← Back to Home</a></p>
            </div>

            <div id="successMessage" class="message success-message">
                <i class="fas fa-check-circle"></i>
                <h3>Account Created Successfully!</h3>
                <p>Please check your email to verify your account, then you can login.</p>
            </div>

            <div id="errorMessage" class="message error-message-box">
                <i class="fas fa-exclamation-circle"></i>
                <h3>Registration Failed</h3>
                <p id="errorText">Please try again.</p>
            </div>
        </div>
    </div>

    <script src="https://cdn.jsdelivr.net/npm/@supabase/supabase-js@2"></script>
    <script src="config.js"></script>
    <script src="auth.js"></script>
</body>
</html>
