// ServicePro - Supabase Configuration
// ✅ Supabase credentials are already configured!

const SUPABASE_CONFIG = {
    url: 'https://pabikxyftoymhvzdqbhz.supabase.co',
    anonKey: 'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6InBhYmlreHlmdG95bWh2emRxYmh6Iiwicm9sZSI6ImFub24iLCJpYXQiOjE3NTE5Njk5MzUsImV4cCI6MjA2NzU0NTkzNX0.Wl8w02Q5XfDEqRvoIE4uobXXUXo3NJhCY8ZcjyYs1Xg'
};

// App Configuration
const APP_CONFIG = {
    // App Information
    name: 'ServicePro',
    version: '1.0.0',
    description: 'Professional Service Booking Platform',

    // Environment
    environment: window.location.hostname === 'localhost' ? 'development' : 'production',

    // Features
    features: {
        emailVerification: true,
        phoneVerification: false,
        socialLogin: false,
        fileUploads: true,
        notifications: true
    },

    // Business Configuration
    business: {
        currency: 'USD',
        timezone: 'America/New_York',
        defaultServiceRadius: 25, // miles
        bookingAdvanceLimit: 30, // days
        cancellationPolicy: '24 hours notice required'
    }
};

// Export for global use
window.SUPABASE_CONFIG = SUPABASE_CONFIG;
window.APP_CONFIG = APP_CONFIG;

// Configuration validation
function validateConfig() {
    if (!SUPABASE_CONFIG.url || !SUPABASE_CONFIG.anonKey) {
        console.error('❌ Supabase configuration missing!');
        return false;
    }
    console.log('✅ Supabase configuration loaded successfully!');
    return true;
}

// Auto-validate on load
document.addEventListener('DOMContentLoaded', validateConfig);
