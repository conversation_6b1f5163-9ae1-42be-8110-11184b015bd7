/* Reset and base styles */
* {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
}

body {
    font-family: 'Inter', sans-serif;
    background: linear-gradient(135deg, #f5f7fa 0%, #c3cfe2 100%);
    min-height: 100vh;
    color: #2d3748;
}

.container {
    max-width: 1400px;
    margin: 0 auto;
    padding: 20px;
}

/* Header */
.dashboard-header {
    background: white;
    border-radius: 16px;
    padding: 30px;
    margin-bottom: 30px;
    box-shadow: 0 4px 20px rgba(0, 0, 0, 0.08);
}

.header-content {
    display: flex;
    justify-content: space-between;
    align-items: center;
    flex-wrap: wrap;
    gap: 20px;
}

.dashboard-header h1 {
    font-size: 2rem;
    font-weight: 700;
    color: #2d3748;
}

.dashboard-header h1 i {
    color: #667eea;
    margin-right: 12px;
}

.header-actions {
    display: flex;
    gap: 12px;
}

/* Buttons */
.btn {
    padding: 12px 20px;
    border: none;
    border-radius: 8px;
    font-size: 0.9rem;
    font-weight: 500;
    cursor: pointer;
    text-decoration: none;
    display: inline-flex;
    align-items: center;
    gap: 8px;
    transition: all 0.3s ease;
}

.btn-primary {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    color: white;
}

.btn-primary:hover {
    transform: translateY(-2px);
    box-shadow: 0 8px 25px rgba(102, 126, 234, 0.3);
}

.btn-secondary {
    background: #e2e8f0;
    color: #4a5568;
}

.btn-secondary:hover {
    background: #cbd5e0;
}

.btn-outline {
    background: transparent;
    border: 2px solid #e2e8f0;
    color: #4a5568;
}

.btn-outline:hover {
    border-color: #667eea;
    color: #667eea;
}

/* Stats Grid */
.stats-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
    gap: 20px;
    margin-bottom: 30px;
}

.stat-card {
    background: white;
    border-radius: 16px;
    padding: 25px;
    display: flex;
    align-items: center;
    gap: 20px;
    box-shadow: 0 4px 20px rgba(0, 0, 0, 0.08);
    transition: transform 0.3s ease;
}

.stat-card:hover {
    transform: translateY(-4px);
}

.stat-icon {
    width: 60px;
    height: 60px;
    border-radius: 12px;
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    display: flex;
    align-items: center;
    justify-content: center;
    color: white;
    font-size: 1.5rem;
}

.stat-content h3 {
    font-size: 2rem;
    font-weight: 700;
    color: #2d3748;
    margin-bottom: 4px;
}

.stat-content p {
    color: #718096;
    font-size: 0.9rem;
}

/* Controls Section */
.controls-section {
    background: white;
    border-radius: 16px;
    padding: 25px;
    margin-bottom: 30px;
    box-shadow: 0 4px 20px rgba(0, 0, 0, 0.08);
}

.search-controls {
    display: flex;
    flex-direction: column;
    gap: 20px;
}

.search-box {
    position: relative;
    flex: 1;
    max-width: 500px;
}

.search-box i {
    position: absolute;
    left: 15px;
    top: 50%;
    transform: translateY(-50%);
    color: #a0aec0;
}

.search-box input {
    width: 100%;
    padding: 15px 15px 15px 45px;
    border: 2px solid #e2e8f0;
    border-radius: 12px;
    font-size: 1rem;
    transition: all 0.3s ease;
}

.search-box input:focus {
    outline: none;
    border-color: #667eea;
    box-shadow: 0 0 0 3px rgba(102, 126, 234, 0.1);
}

.clear-btn {
    position: absolute;
    right: 10px;
    top: 50%;
    transform: translateY(-50%);
    background: none;
    border: none;
    color: #a0aec0;
    cursor: pointer;
    padding: 5px;
    border-radius: 4px;
}

.clear-btn:hover {
    color: #e53e3e;
    background: #fed7d7;
}

.filter-controls {
    display: flex;
    gap: 15px;
    flex-wrap: wrap;
    align-items: center;
}

.filter-controls select,
.filter-controls input[type="date"] {
    padding: 12px 15px;
    border: 2px solid #e2e8f0;
    border-radius: 8px;
    font-size: 0.9rem;
    min-width: 150px;
}

.filter-controls select:focus,
.filter-controls input[type="date"]:focus {
    outline: none;
    border-color: #667eea;
}

/* Table Section */
.table-section {
    background: white;
    border-radius: 16px;
    overflow: hidden;
    box-shadow: 0 4px 20px rgba(0, 0, 0, 0.08);
}

.table-header {
    padding: 25px;
    border-bottom: 1px solid #e2e8f0;
    display: flex;
    justify-content: space-between;
    align-items: center;
}

.table-header h2 {
    font-size: 1.5rem;
    font-weight: 600;
    color: #2d3748;
}

.table-info {
    color: #718096;
    font-size: 0.9rem;
}

.table-container {
    overflow-x: auto;
}

.submissions-table {
    width: 100%;
    border-collapse: collapse;
}

.submissions-table th {
    background: #f7fafc;
    padding: 15px;
    text-align: left;
    font-weight: 600;
    color: #4a5568;
    border-bottom: 1px solid #e2e8f0;
    cursor: pointer;
    user-select: none;
    white-space: nowrap;
}

.submissions-table th:hover {
    background: #edf2f7;
}

.submissions-table th i {
    margin-left: 8px;
    opacity: 0.5;
}

.submissions-table td {
    padding: 15px;
    border-bottom: 1px solid #f1f5f9;
    vertical-align: top;
}

.submissions-table tbody tr:hover {
    background: #f7fafc;
}

.name-cell {
    font-weight: 500;
    color: #2d3748;
}

.email-cell {
    color: #667eea;
}

.subject-badge {
    display: inline-block;
    padding: 4px 12px;
    border-radius: 20px;
    font-size: 0.8rem;
    font-weight: 500;
    text-transform: capitalize;
}

.subject-general { background: #e6fffa; color: #234e52; }
.subject-support { background: #fef5e7; color: #744210; }
.subject-sales { background: #e6f3ff; color: #1a365d; }
.subject-partnership { background: #f0fff4; color: #22543d; }
.subject-feedback { background: #faf5ff; color: #553c9a; }
.subject-other { background: #f7fafc; color: #4a5568; }

.newsletter-badge {
    display: inline-flex;
    align-items: center;
    gap: 4px;
    font-size: 0.8rem;
}

.newsletter-yes { color: #38a169; }
.newsletter-no { color: #a0aec0; }

.action-buttons {
    display: flex;
    gap: 8px;
}

.action-btn {
    padding: 6px 10px;
    border: none;
    border-radius: 6px;
    cursor: pointer;
    font-size: 0.8rem;
    transition: all 0.2s ease;
}

.view-btn {
    background: #e6f3ff;
    color: #1a365d;
}

.view-btn:hover {
    background: #bee3f8;
}

/* Table Footer */
.table-footer {
    padding: 20px 25px;
    border-top: 1px solid #e2e8f0;
    display: flex;
    justify-content: center;
}

.pagination {
    display: flex;
    align-items: center;
    gap: 15px;
}

/* Loading Overlay */
.loading-overlay {
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: rgba(255, 255, 255, 0.9);
    display: flex;
    align-items: center;
    justify-content: center;
    z-index: 1000;
}

.loading-spinner {
    text-align: center;
}

.loading-spinner i {
    font-size: 3rem;
    color: #667eea;
    margin-bottom: 15px;
}

.loading-spinner p {
    color: #4a5568;
    font-size: 1.1rem;
}

/* Modal */
.modal {
    display: none;
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: rgba(0, 0, 0, 0.5);
    z-index: 2000;
    align-items: center;
    justify-content: center;
}

.modal.active {
    display: flex;
}

.modal-content {
    background: white;
    border-radius: 16px;
    max-width: 600px;
    width: 90%;
    max-height: 80vh;
    overflow-y: auto;
    box-shadow: 0 20px 40px rgba(0, 0, 0, 0.2);
}

.modal-header {
    padding: 25px;
    border-bottom: 1px solid #e2e8f0;
    display: flex;
    justify-content: space-between;
    align-items: center;
}

.modal-header h3 {
    font-size: 1.5rem;
    font-weight: 600;
    color: #2d3748;
}

.modal-close {
    background: none;
    border: none;
    font-size: 1.5rem;
    color: #a0aec0;
    cursor: pointer;
    padding: 5px;
    border-radius: 4px;
}

.modal-close:hover {
    color: #e53e3e;
    background: #fed7d7;
}

.modal-body {
    padding: 25px;
}

.modal-footer {
    padding: 25px;
    border-top: 1px solid #e2e8f0;
    display: flex;
    justify-content: flex-end;
    gap: 12px;
}

.detail-group {
    margin-bottom: 20px;
}

.detail-label {
    font-weight: 600;
    color: #4a5568;
    margin-bottom: 5px;
    display: block;
}

.detail-value {
    color: #2d3748;
    line-height: 1.6;
}

.message-content {
    background: #f7fafc;
    padding: 15px;
    border-radius: 8px;
    border-left: 4px solid #667eea;
    white-space: pre-wrap;
}

/* Empty state */
.empty-state {
    text-align: center;
    padding: 60px 20px;
    color: #718096;
}

.empty-state i {
    font-size: 4rem;
    margin-bottom: 20px;
    color: #e2e8f0;
}

.empty-state h3 {
    font-size: 1.5rem;
    margin-bottom: 10px;
    color: #4a5568;
}

.empty-state p {
    font-size: 1rem;
    line-height: 1.6;
}

/* Responsive Design */
@media (max-width: 768px) {
    .container {
        padding: 15px;
    }

    .header-content {
        flex-direction: column;
        align-items: stretch;
    }

    .search-controls {
        flex-direction: column;
    }

    .filter-controls {
        flex-direction: column;
        align-items: stretch;
    }

    .filter-controls select,
    .filter-controls input[type="date"] {
        min-width: auto;
    }

    .table-container {
        font-size: 0.9rem;
    }

    .submissions-table th,
    .submissions-table td {
        padding: 10px 8px;
    }

    .stats-grid {
        grid-template-columns: 1fr;
    }
}
