// Supabase configuration - Make sure to create config.js from config.example.js
let supabaseClient;

// Initialize Supabase client
function initializeSupabase() {
    if (window.SUPABASE_CONFIG) {
        supabaseClient = supabase.createClient(
            window.SUPABASE_CONFIG.url,
            window.SUPABASE_CONFIG.anonKey
        );
        return true;
    } else {
        // Fallback to direct configuration (less secure)
        const SUPABASE_URL = 'YOUR_SUPABASE_URL';
        const SUPABASE_ANON_KEY = 'YOUR_SUPABASE_ANON_KEY';

        if (SUPABASE_URL === 'YOUR_SUPABASE_URL' || SUPABASE_ANON_KEY === 'YOUR_SUPABASE_ANON_KEY') {
            showError('Please configure your Supabase credentials. See README.md for instructions.');
            return false;
        }

        supabaseClient = supabase.createClient(SUPABASE_URL, SUPABASE_ANON_KEY);
        return true;
    }
}

// Form elements
const form = document.getElementById('contactForm');
const submitBtn = form.querySelector('.submit-btn');
const successMessage = document.getElementById('successMessage');
const errorMessage = document.getElementById('errorMessage');
const errorText = document.getElementById('errorText');

// Form validation rules
const validationRules = {
    firstName: {
        required: true,
        minLength: 2,
        pattern: /^[a-zA-Z\s]+$/,
        message: 'First name must be at least 2 characters and contain only letters'
    },
    lastName: {
        required: true,
        minLength: 2,
        pattern: /^[a-zA-Z\s]+$/,
        message: 'Last name must be at least 2 characters and contain only letters'
    },
    email: {
        required: true,
        pattern: /^[^\s@]+@[^\s@]+\.[^\s@]+$/,
        message: 'Please enter a valid email address'
    },
    phone: {
        required: false,
        pattern: /^[\+]?[1-9][\d]{0,15}$/,
        message: 'Please enter a valid phone number'
    },
    company: {
        required: false,
        minLength: 2,
        message: 'Company name must be at least 2 characters'
    },
    subject: {
        required: true,
        message: 'Please select a subject'
    },
    message: {
        required: true,
        minLength: 10,
        maxLength: 1000,
        message: 'Message must be between 10 and 1000 characters'
    }
};

// Initialize the application
document.addEventListener('DOMContentLoaded', function() {
    if (initializeSupabase()) {
        initializeDatabase();
        setupFormValidation();
        setupFormSubmission();
    }
});

// Create database table if it doesn't exist
async function initializeDatabase() {
    try {
        // Check if table exists by trying to select from it
        const { data, error } = await supabaseClient
            .from('contact_submissions')
            .select('id')
            .limit(1);

        if (error && error.code === 'PGRST116') {
            // Table doesn't exist, create it
            console.log('Creating contact_submissions table...');
            await createContactTable();
        }
    } catch (error) {
        console.error('Error checking/creating database:', error);
    }
}

// Create the contact submissions table
async function createContactTable() {
    try {
        // Note: In a real application, you would typically create tables through the Supabase dashboard
        // or using SQL commands. This is a simplified approach for demonstration.
        
        const { data, error } = await supabaseClient.rpc('create_contact_table');
        
        if (error) {
            console.error('Error creating table:', error);
            // If RPC doesn't work, we'll handle table creation through the dashboard
            showError('Database setup required. Please create the contact_submissions table in your Supabase dashboard.');
        } else {
            console.log('Table created successfully');
        }
    } catch (error) {
        console.error('Error in createContactTable:', error);
    }
}

// Setup form validation
function setupFormValidation() {
    // Add real-time validation to form fields
    Object.keys(validationRules).forEach(fieldName => {
        const field = document.getElementById(fieldName);
        if (field) {
            field.addEventListener('blur', () => validateField(fieldName));
            field.addEventListener('input', () => clearFieldError(fieldName));
        }
    });
}

// Setup form submission
function setupFormSubmission() {
    form.addEventListener('submit', handleFormSubmit);
}

// Handle form submission
async function handleFormSubmit(event) {
    event.preventDefault();
    
    // Validate all fields
    const isValid = validateForm();
    if (!isValid) {
        return;
    }
    
    // Show loading state
    setLoadingState(true);
    hideMessages();
    
    try {
        // Collect form data
        const formData = collectFormData();

        // Submit to Supabase
        const { data, error } = await supabaseClient
            .from('contact_submissions')
            .insert([formData]);

        if (error) {
            throw error;
        }

        // Show success message
        showSuccess();
        resetForm();

    } catch (error) {
        console.error('Error submitting form:', error);
        showError(error.message || 'Failed to submit form. Please try again.');
    } finally {
        setLoadingState(false);
    }
}

// Validate entire form
function validateForm() {
    let isValid = true;
    
    Object.keys(validationRules).forEach(fieldName => {
        if (!validateField(fieldName)) {
            isValid = false;
        }
    });
    
    return isValid;
}

// Validate individual field
function validateField(fieldName) {
    const field = document.getElementById(fieldName);
    const rules = validationRules[fieldName];
    const value = field.value.trim();
    
    // Check required fields
    if (rules.required && !value) {
        showFieldError(fieldName, `${getFieldLabel(fieldName)} is required`);
        return false;
    }
    
    // Skip other validations if field is empty and not required
    if (!value && !rules.required) {
        clearFieldError(fieldName);
        return true;
    }
    
    // Check minimum length
    if (rules.minLength && value.length < rules.minLength) {
        showFieldError(fieldName, rules.message);
        return false;
    }
    
    // Check maximum length
    if (rules.maxLength && value.length > rules.maxLength) {
        showFieldError(fieldName, rules.message);
        return false;
    }
    
    // Check pattern
    if (rules.pattern && !rules.pattern.test(value)) {
        showFieldError(fieldName, rules.message);
        return false;
    }
    
    clearFieldError(fieldName);
    return true;
}

// Show field error
function showFieldError(fieldName, message) {
    const formGroup = document.getElementById(fieldName).closest('.form-group');
    const errorElement = formGroup.querySelector('.error-message');
    
    formGroup.classList.add('error');
    errorElement.textContent = message;
}

// Clear field error
function clearFieldError(fieldName) {
    const formGroup = document.getElementById(fieldName).closest('.form-group');
    const errorElement = formGroup.querySelector('.error-message');
    
    formGroup.classList.remove('error');
    errorElement.textContent = '';
}

// Get field label for error messages
function getFieldLabel(fieldName) {
    const labels = {
        firstName: 'First Name',
        lastName: 'Last Name',
        email: 'Email',
        phone: 'Phone',
        company: 'Company',
        subject: 'Subject',
        message: 'Message'
    };
    return labels[fieldName] || fieldName;
}

// Collect form data
function collectFormData() {
    return {
        first_name: document.getElementById('firstName').value.trim(),
        last_name: document.getElementById('lastName').value.trim(),
        email: document.getElementById('email').value.trim(),
        phone: document.getElementById('phone').value.trim() || null,
        company: document.getElementById('company').value.trim() || null,
        subject: document.getElementById('subject').value,
        message: document.getElementById('message').value.trim(),
        newsletter_subscription: document.getElementById('newsletter').checked,
        submitted_at: new Date().toISOString()
    };
}

// Set loading state
function setLoadingState(loading) {
    if (loading) {
        submitBtn.classList.add('loading');
        submitBtn.disabled = true;
    } else {
        submitBtn.classList.remove('loading');
        submitBtn.disabled = false;
    }
}

// Show success message
function showSuccess() {
    hideMessages();
    successMessage.style.display = 'block';
    successMessage.scrollIntoView({ behavior: 'smooth' });
}

// Show error message
function showError(message) {
    hideMessages();
    errorText.textContent = message;
    errorMessage.style.display = 'block';
    errorMessage.scrollIntoView({ behavior: 'smooth' });
}

// Hide all messages
function hideMessages() {
    successMessage.style.display = 'none';
    errorMessage.style.display = 'none';
}

// Reset form
function resetForm() {
    form.reset();
    
    // Clear all error states
    Object.keys(validationRules).forEach(fieldName => {
        clearFieldError(fieldName);
    });
}

// Utility function to create table (for demonstration)
// In a real application, you would create this table through Supabase dashboard
function getCreateTableSQL() {
    return `
        CREATE TABLE IF NOT EXISTS contact_submissions (
            id SERIAL PRIMARY KEY,
            first_name VARCHAR(100) NOT NULL,
            last_name VARCHAR(100) NOT NULL,
            email VARCHAR(255) NOT NULL,
            phone VARCHAR(20),
            company VARCHAR(255),
            subject VARCHAR(100) NOT NULL,
            message TEXT NOT NULL,
            newsletter_subscription BOOLEAN DEFAULT FALSE,
            submitted_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
            created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
        );
    `;
}
