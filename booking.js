// Booking Management Module for Service Booking App
class BookingManager {
    constructor() {
        this.supabase = null;
        this.currentUser = null;
        this.init();
    }

    async init() {
        // Wait for auth manager to be available
        if (window.authManager) {
            this.supabase = window.authManager.getSupabaseClient();
            this.currentUser = await window.authManager.getCurrentUser();
            this.initializeBookingPage();
        } else {
            // Retry after a short delay
            setTimeout(() => this.init(), 100);
        }
    }

    initializeBookingPage() {
        // Only initialize on dashboard page
        if (window.location.pathname.includes('dashboard.html') || 
            window.location.pathname.endsWith('dashboard.html')) {
            
            this.setupBookingForm();
            this.loadUserBookings();
            this.setMinDate();
        }
    }

    setupBookingForm() {
        const form = document.getElementById('bookingForm');
        if (form) {
            form.addEventListener('submit', (e) => this.handleBookingSubmit(e));
        }
    }

    setMinDate() {
        // Set minimum date to today
        const dateInput = document.getElementById('bookingDate');
        if (dateInput) {
            const today = new Date().toISOString().split('T')[0];
            dateInput.min = today;
        }
    }

    async handleBookingSubmit(event) {
        event.preventDefault();
        
        const form = event.target;
        const formData = new FormData(form);
        
        const bookingData = {
            service_type: formData.get('serviceType'),
            booking_date: formData.get('bookingDate'),
            booking_time: formData.get('bookingTime'),
            notes: formData.get('notes') || null,
            user_id: this.currentUser.id
        };

        // Clear previous errors
        this.clearErrors();

        // Validate booking data
        if (!this.validateBooking(bookingData)) {
            return;
        }

        // Show loading state
        this.setFormLoading(form, true);

        try {
            const { data, error } = await this.supabase
                .from('bookings')
                .insert([bookingData])
                .select();

            if (error) throw error;

            // Show success message
            this.showMessage('successMessage');
            form.reset();
            this.setMinDate(); // Reset min date after form reset
            
            // Reload bookings list
            await this.loadUserBookings();

        } catch (error) {
            console.error('Booking error:', error);
            this.showError(error.message);
        } finally {
            this.setFormLoading(form, false);
        }
    }

    validateBooking(bookingData) {
        let isValid = true;

        // Validate service type
        if (!bookingData.service_type) {
            this.showFieldError('serviceType', 'Please select a service type');
            isValid = false;
        }

        // Validate date
        if (!bookingData.booking_date) {
            this.showFieldError('bookingDate', 'Please select a date');
            isValid = false;
        } else {
            const selectedDate = new Date(bookingData.booking_date);
            const today = new Date();
            today.setHours(0, 0, 0, 0);
            
            if (selectedDate < today) {
                this.showFieldError('bookingDate', 'Please select a future date');
                isValid = false;
            }
        }

        // Validate time
        if (!bookingData.booking_time) {
            this.showFieldError('bookingTime', 'Please select a time');
            isValid = false;
        }

        return isValid;
    }

    async loadUserBookings() {
        const loadingElement = document.getElementById('loadingBookings');
        const noBookingsElement = document.getElementById('noBookings');
        const bookingsListElement = document.getElementById('bookingsList');

        // Show loading state
        if (loadingElement) loadingElement.style.display = 'block';
        if (noBookingsElement) noBookingsElement.style.display = 'none';
        if (bookingsListElement) bookingsListElement.innerHTML = '';

        try {
            const { data: bookings, error } = await this.supabase
                .from('bookings')
                .select('*')
                .eq('user_id', this.currentUser.id)
                .order('booking_date', { ascending: true })
                .order('booking_time', { ascending: true });

            if (error) throw error;

            // Hide loading state
            if (loadingElement) loadingElement.style.display = 'none';

            if (bookings && bookings.length > 0) {
                this.displayBookings(bookings);
            } else {
                if (noBookingsElement) noBookingsElement.style.display = 'block';
            }

        } catch (error) {
            console.error('Error loading bookings:', error);
            if (loadingElement) loadingElement.style.display = 'none';
            this.showError('Failed to load bookings. Please refresh the page.');
        }
    }

    displayBookings(bookings) {
        const bookingsListElement = document.getElementById('bookingsList');
        if (!bookingsListElement) return;

        bookingsListElement.innerHTML = '';

        bookings.forEach(booking => {
            const bookingCard = this.createBookingCard(booking);
            bookingsListElement.appendChild(bookingCard);
        });
    }

    createBookingCard(booking) {
        const card = document.createElement('div');
        card.className = 'booking-card';
        card.setAttribute('data-booking-id', booking.id);

        const date = new Date(booking.booking_date).toLocaleDateString('en-US', {
            weekday: 'long',
            year: 'numeric',
            month: 'long',
            day: 'numeric'
        });

        const time = new Date(`2000-01-01T${booking.booking_time}`).toLocaleTimeString('en-US', {
            hour: 'numeric',
            minute: '2-digit',
            hour12: true
        });

        const statusIcon = this.getStatusIcon(booking.status);
        const statusClass = `status-${booking.status}`;

        card.innerHTML = `
            <div class="booking-header">
                <div class="booking-service">
                    <i class="fas fa-cogs"></i>
                    <span class="service-name">${this.formatServiceType(booking.service_type)}</span>
                </div>
                <div class="booking-status ${statusClass}">
                    <i class="${statusIcon}"></i>
                    <span>${this.formatStatus(booking.status)}</span>
                </div>
            </div>
            <div class="booking-details">
                <div class="booking-datetime">
                    <div class="booking-date">
                        <i class="fas fa-calendar"></i>
                        <span>${date}</span>
                    </div>
                    <div class="booking-time">
                        <i class="fas fa-clock"></i>
                        <span>${time}</span>
                    </div>
                </div>
                ${booking.notes ? `
                    <div class="booking-notes">
                        <i class="fas fa-sticky-note"></i>
                        <span>${booking.notes}</span>
                    </div>
                ` : ''}
            </div>
            <div class="booking-actions">
                <button class="btn btn-outline btn-sm" onclick="bookingManager.cancelBooking('${booking.id}')">
                    <i class="fas fa-times"></i>
                    Cancel
                </button>
            </div>
        `;

        return card;
    }

    async cancelBooking(bookingId) {
        if (!confirm('Are you sure you want to cancel this booking?')) {
            return;
        }

        try {
            const { error } = await this.supabase
                .from('bookings')
                .update({ status: 'cancelled' })
                .eq('id', bookingId)
                .eq('user_id', this.currentUser.id);

            if (error) throw error;

            // Reload bookings to reflect the change
            await this.loadUserBookings();

        } catch (error) {
            console.error('Error cancelling booking:', error);
            alert('Failed to cancel booking. Please try again.');
        }
    }

    // Utility methods
    formatServiceType(serviceType) {
        return serviceType.charAt(0).toUpperCase() + serviceType.slice(1);
    }

    formatStatus(status) {
        return status.charAt(0).toUpperCase() + status.slice(1);
    }

    getStatusIcon(status) {
        const icons = {
            pending: 'fas fa-clock',
            confirmed: 'fas fa-check-circle',
            cancelled: 'fas fa-times-circle',
            completed: 'fas fa-check-double'
        };
        return icons[status] || 'fas fa-question-circle';
    }

    showMessage(messageId) {
        const message = document.getElementById(messageId);
        if (message) {
            message.style.display = 'block';
            setTimeout(() => {
                message.style.display = 'none';
            }, 5000);
        }
    }

    showError(errorText) {
        const errorMessage = document.getElementById('errorMessage');
        const errorTextElement = document.getElementById('errorText');
        
        if (errorMessage && errorTextElement) {
            errorTextElement.textContent = errorText;
            errorMessage.style.display = 'block';
            setTimeout(() => {
                errorMessage.style.display = 'none';
            }, 5000);
        }
    }

    showFieldError(fieldName, errorText) {
        const field = document.getElementById(fieldName);
        if (field) {
            const errorSpan = field.parentNode.querySelector('.error-message');
            if (errorSpan) {
                errorSpan.textContent = errorText;
                field.classList.add('error');
            }
        }
    }

    clearErrors() {
        const errorMessages = document.querySelectorAll('.error-message');
        errorMessages.forEach(span => span.textContent = '');
        
        const errorFields = document.querySelectorAll('.error');
        errorFields.forEach(field => field.classList.remove('error'));
        
        const messageBoxes = document.querySelectorAll('.message');
        messageBoxes.forEach(box => box.style.display = 'none');
    }

    setFormLoading(form, isLoading) {
        const submitBtn = form.querySelector('.submit-btn');
        const btnText = submitBtn.querySelector('.btn-text');
        const btnLoader = submitBtn.querySelector('.btn-loader');
        
        if (isLoading) {
            submitBtn.disabled = true;
            btnText.style.display = 'none';
            btnLoader.style.display = 'inline-block';
        } else {
            submitBtn.disabled = false;
            btnText.style.display = 'inline-block';
            btnLoader.style.display = 'none';
        }
    }
}

// Initialize booking manager when DOM is loaded
document.addEventListener('DOMContentLoaded', () => {
    window.bookingManager = new BookingManager();
});
