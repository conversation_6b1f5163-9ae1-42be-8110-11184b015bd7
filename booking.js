// ServicePro - Enhanced Booking Management Module
class BookingManager {
    constructor() {
        this.supabase = null;
        this.authManager = null;
        this.currentUser = null;
        this.userProfile = null;
        this.services = [];
        this.serviceCategories = [];
        this.init();
    }

    async init() {
        try {
            // Wait for auth manager to be available
            if (window.authManager) {
                this.authManager = window.authManager;
                this.supabase = this.authManager.getSupabaseClient();
                this.currentUser = await this.authManager.getCurrentUser();
                this.userProfile = this.authManager.getCurrentUserProfile();

                await this.loadServiceData();
                this.initializeBookingPage();

                console.log('✅ Booking Manager initialized successfully');
            } else {
                // Retry after a short delay
                setTimeout(() => this.init(), 100);
            }
        } catch (error) {
            console.error('❌ Error initializing Booking Manager:', error);
        }
    }

    async loadServiceData() {
        try {
            // Load service categories
            const { data: categories, error: categoriesError } = await this.supabase
                .from('service_categories')
                .select('*')
                .eq('is_active', true)
                .order('sort_order');

            if (categoriesError) throw categoriesError;
            this.serviceCategories = categories || [];

            // Load services
            const { data: services, error: servicesError } = await this.supabase
                .from('services')
                .select(`
                    *,
                    service_categories (
                        id,
                        name,
                        icon,
                        color
                    )
                `)
                .eq('is_active', true)
                .order('name');

            if (servicesError) throw servicesError;
            this.services = services || [];

            console.log('✅ Service data loaded:', {
                categories: this.serviceCategories.length,
                services: this.services.length
            });

        } catch (error) {
            console.error('❌ Error loading service data:', error);
        }
    }

    initializeBookingPage() {
        // Only initialize on dashboard page
        if (window.location.pathname.includes('dashboard.html') ||
            window.location.pathname.endsWith('dashboard.html')) {

            this.setupBookingForm();
            this.populateServiceOptions();
            this.loadUserBookings();
            this.setMinDate();
        }
    }

    setupBookingForm() {
        const form = document.getElementById('bookingForm');
        if (form) {
            form.addEventListener('submit', (e) => this.handleBookingSubmit(e));
        }

        // Setup service type change handler
        const serviceSelect = document.getElementById('serviceType');
        if (serviceSelect) {
            serviceSelect.addEventListener('change', (e) => this.handleServiceTypeChange(e));
        }
    }

    populateServiceOptions() {
        const serviceSelect = document.getElementById('serviceType');
        if (!serviceSelect || this.services.length === 0) return;

        // Clear existing options except the first one
        serviceSelect.innerHTML = '<option value="">Select a service</option>';

        // Group services by category
        const servicesByCategory = {};
        this.services.forEach(service => {
            const categoryName = service.service_categories?.name || 'Other';
            if (!servicesByCategory[categoryName]) {
                servicesByCategory[categoryName] = [];
            }
            servicesByCategory[categoryName].push(service);
        });

        // Add options grouped by category
        Object.keys(servicesByCategory).forEach(categoryName => {
            const optgroup = document.createElement('optgroup');
            optgroup.label = categoryName;

            servicesByCategory[categoryName].forEach(service => {
                const option = document.createElement('option');
                option.value = service.id;
                option.textContent = `${service.name} - $${service.base_price}${service.price_type === 'hourly' ? '/hr' : ''}`;
                option.dataset.price = service.base_price;
                option.dataset.priceType = service.price_type;
                option.dataset.duration = service.duration_minutes;
                optgroup.appendChild(option);
            });

            serviceSelect.appendChild(optgroup);
        });
    }

    handleServiceTypeChange(event) {
        const selectedOption = event.target.selectedOptions[0];
        if (!selectedOption || !selectedOption.value) return;

        // Update duration if available
        const duration = selectedOption.dataset.duration;
        if (duration) {
            // You can add duration display logic here
            console.log('Selected service duration:', duration, 'minutes');
        }

        // Update price display if available
        const price = selectedOption.dataset.price;
        const priceType = selectedOption.dataset.priceType;
        if (price) {
            console.log('Selected service price:', price, priceType);
        }
    }

    setMinDate() {
        // Set minimum date to today
        const dateInput = document.getElementById('bookingDate');
        if (dateInput) {
            const today = new Date().toISOString().split('T')[0];
            dateInput.min = today;

            // Set maximum date based on advance booking limit
            const maxDate = new Date();
            maxDate.setDate(maxDate.getDate() + (window.APP_CONFIG?.business?.bookingAdvanceLimit || 30));
            dateInput.max = maxDate.toISOString().split('T')[0];
        }
    }

    async handleBookingSubmit(event) {
        event.preventDefault();

        const form = event.target;
        const formData = new FormData(form);

        // Get selected service details
        const serviceId = formData.get('serviceType');
        const selectedService = this.services.find(s => s.id === serviceId);

        const bookingData = {
            user_id: this.currentUser.id,
            service_id: serviceId,
            booking_date: formData.get('bookingDate'),
            booking_time: formData.get('bookingTime'),
            duration_minutes: selectedService?.duration_minutes || 60,
            quoted_price: selectedService?.base_price || null,
            currency: 'USD',
            customer_notes: formData.get('notes') || null,
            status: 'pending',

            // Location data (if provided)
            service_address: formData.get('address') || null,
            service_city: formData.get('city') || null,
            service_state: formData.get('state') || null,
            service_postal_code: formData.get('postalCode') || null
        };

        // Clear previous errors
        this.clearErrors();

        // Validate booking data
        if (!this.validateBooking(bookingData, selectedService)) {
            return;
        }

        // Show loading state
        this.setFormLoading(form, true);

        try {
            const { data, error } = await this.supabase
                .from('bookings')
                .insert([bookingData])
                .select(`
                    *,
                    services (
                        id,
                        name,
                        service_categories (
                            name,
                            icon
                        )
                    )
                `);

            if (error) throw error;

            // Show success message
            this.showMessage('successMessage');
            form.reset();
            this.setMinDate(); // Reset min date after form reset

            // Reload bookings list
            await this.loadUserBookings();

            console.log('✅ Booking created successfully:', data[0]);

        } catch (error) {
            console.error('❌ Booking error:', error);
            this.showError(this.getErrorMessage(error));
        } finally {
            this.setFormLoading(form, false);
        }
    }

    validateBooking(bookingData, selectedService) {
        let isValid = true;

        // Validate service selection
        if (!bookingData.service_id) {
            this.showFieldError('serviceType', 'Please select a service');
            isValid = false;
        }

        // Validate date
        if (!bookingData.booking_date) {
            this.showFieldError('bookingDate', 'Please select a date');
            isValid = false;
        } else {
            const selectedDate = new Date(bookingData.booking_date);
            const today = new Date();
            today.setHours(0, 0, 0, 0);

            if (selectedDate < today) {
                this.showFieldError('bookingDate', 'Please select a future date');
                isValid = false;
            }

            // Check advance booking limit
            const maxAdvanceDays = selectedService?.max_advance_booking_days || 30;
            const maxDate = new Date();
            maxDate.setDate(maxDate.getDate() + maxAdvanceDays);
            maxDate.setHours(0, 0, 0, 0);

            if (selectedDate > maxDate) {
                this.showFieldError('bookingDate', `Bookings can only be made ${maxAdvanceDays} days in advance`);
                isValid = false;
            }
        }

        // Validate time
        if (!bookingData.booking_time) {
            this.showFieldError('bookingTime', 'Please select a time');
            isValid = false;
        } else {
            // Check minimum advance booking time
            const minAdvanceHours = selectedService?.min_advance_booking_hours || 2;
            const selectedDateTime = new Date(`${bookingData.booking_date}T${bookingData.booking_time}`);
            const minDateTime = new Date();
            minDateTime.setHours(minDateTime.getHours() + minAdvanceHours);

            if (selectedDateTime < minDateTime) {
                this.showFieldError('bookingTime', `Bookings must be made at least ${minAdvanceHours} hours in advance`);
                isValid = false;
            }
        }

        return isValid;
    }

    async loadUserBookings() {
        const loadingElement = document.getElementById('loadingBookings');
        const noBookingsElement = document.getElementById('noBookings');
        const bookingsListElement = document.getElementById('bookingsList');

        // Show loading state
        if (loadingElement) loadingElement.style.display = 'block';
        if (noBookingsElement) noBookingsElement.style.display = 'none';
        if (bookingsListElement) bookingsListElement.innerHTML = '';

        try {
            const { data: bookings, error } = await this.supabase
                .from('bookings')
                .select(`
                    *,
                    services (
                        id,
                        name,
                        description,
                        service_categories (
                            name,
                            icon,
                            color
                        )
                    ),
                    service_providers (
                        id,
                        business_name,
                        rating_average,
                        user_profiles (
                            first_name,
                            last_name
                        )
                    )
                `)
                .eq('user_id', this.currentUser.id)
                .order('booking_date', { ascending: false })
                .order('booking_time', { ascending: false });

            if (error) throw error;

            // Hide loading state
            if (loadingElement) loadingElement.style.display = 'none';

            if (bookings && bookings.length > 0) {
                this.displayBookings(bookings);
                console.log('✅ Loaded', bookings.length, 'bookings');
            } else {
                if (noBookingsElement) noBookingsElement.style.display = 'block';
                console.log('📝 No bookings found');
            }

        } catch (error) {
            console.error('❌ Error loading bookings:', error);
            if (loadingElement) loadingElement.style.display = 'none';
            this.showError('Failed to load bookings. Please refresh the page.');
        }
    }

    displayBookings(bookings) {
        const bookingsListElement = document.getElementById('bookingsList');
        if (!bookingsListElement) return;

        bookingsListElement.innerHTML = '';

        bookings.forEach(booking => {
            const bookingCard = this.createBookingCard(booking);
            bookingsListElement.appendChild(bookingCard);
        });
    }

    createBookingCard(booking) {
        const card = document.createElement('div');
        card.className = 'booking-card';
        card.setAttribute('data-booking-id', booking.id);

        const date = new Date(booking.booking_date).toLocaleDateString('en-US', {
            weekday: 'long',
            year: 'numeric',
            month: 'long',
            day: 'numeric'
        });

        const time = new Date(`2000-01-01T${booking.booking_time}`).toLocaleTimeString('en-US', {
            hour: 'numeric',
            minute: '2-digit',
            hour12: true
        });

        const statusIcon = this.getStatusIcon(booking.status);
        const statusClass = `status-${booking.status}`;

        // Get service information
        const serviceName = booking.services?.name || 'Unknown Service';
        const categoryIcon = booking.services?.service_categories?.icon || 'fas fa-cogs';
        const categoryColor = booking.services?.service_categories?.color || '#3B82F6';

        // Get provider information
        const providerName = booking.service_providers?.business_name ||
                           (booking.service_providers?.user_profiles ?
                            `${booking.service_providers.user_profiles.first_name} ${booking.service_providers.user_profiles.last_name}` :
                            'Provider TBD');

        // Format price
        const price = booking.quoted_price ? `$${booking.quoted_price}` : 'Price TBD';
        const duration = booking.duration_minutes ? `${booking.duration_minutes} min` : '';

        card.innerHTML = `
            <div class="booking-header">
                <div class="booking-service">
                    <i class="${categoryIcon}" style="color: ${categoryColor}"></i>
                    <div class="service-info">
                        <span class="service-name">${serviceName}</span>
                        ${providerName !== 'Provider TBD' ? `<span class="provider-name">${providerName}</span>` : ''}
                    </div>
                </div>
                <div class="booking-status ${statusClass}">
                    <i class="${statusIcon}"></i>
                    <span>${this.formatStatus(booking.status)}</span>
                </div>
            </div>
            <div class="booking-details">
                <div class="booking-datetime">
                    <div class="booking-date">
                        <i class="fas fa-calendar"></i>
                        <span>${date}</span>
                    </div>
                    <div class="booking-time">
                        <i class="fas fa-clock"></i>
                        <span>${time}${duration ? ` (${duration})` : ''}</span>
                    </div>
                </div>
                ${booking.quoted_price ? `
                    <div class="booking-price">
                        <i class="fas fa-dollar-sign"></i>
                        <span>${price}</span>
                    </div>
                ` : ''}
                ${booking.service_address ? `
                    <div class="booking-location">
                        <i class="fas fa-map-marker-alt"></i>
                        <span>${booking.service_address}</span>
                    </div>
                ` : ''}
                ${booking.customer_notes ? `
                    <div class="booking-notes">
                        <i class="fas fa-sticky-note"></i>
                        <span>${booking.customer_notes}</span>
                    </div>
                ` : ''}
            </div>
            <div class="booking-actions">
                ${this.getBookingActions(booking)}
            </div>
        `;

        return card;
    }

    getBookingActions(booking) {
        const actions = [];

        // Cancel action (only for pending/confirmed bookings)
        if (['pending', 'confirmed'].includes(booking.status)) {
            actions.push(`
                <button class="btn btn-outline btn-sm" onclick="bookingManager.cancelBooking('${booking.id}')">
                    <i class="fas fa-times"></i>
                    Cancel
                </button>
            `);
        }

        // Reschedule action (only for pending/confirmed bookings)
        if (['pending', 'confirmed'].includes(booking.status)) {
            actions.push(`
                <button class="btn btn-outline btn-sm" onclick="bookingManager.rescheduleBooking('${booking.id}')">
                    <i class="fas fa-calendar-alt"></i>
                    Reschedule
                </button>
            `);
        }

        // Review action (only for completed bookings)
        if (booking.status === 'completed') {
            actions.push(`
                <button class="btn btn-primary btn-sm" onclick="bookingManager.reviewBooking('${booking.id}')">
                    <i class="fas fa-star"></i>
                    Review
                </button>
            `);
        }

        return actions.join('');
    }

    async cancelBooking(bookingId) {
        if (!confirm('Are you sure you want to cancel this booking?')) {
            return;
        }

        try {
            const { error } = await this.supabase
                .from('bookings')
                .update({
                    status: 'cancelled',
                    cancelled_at: new Date().toISOString(),
                    cancellation_reason: 'Cancelled by customer'
                })
                .eq('id', bookingId)
                .eq('user_id', this.currentUser.id);

            if (error) throw error;

            // Reload bookings to reflect the change
            await this.loadUserBookings();

            this.showMessage('successMessage');
            console.log('✅ Booking cancelled:', bookingId);

        } catch (error) {
            console.error('❌ Error cancelling booking:', error);
            this.showError('Failed to cancel booking. Please try again.');
        }
    }

    async rescheduleBooking(bookingId) {
        // This would open a modal or redirect to a reschedule form
        console.log('Reschedule booking:', bookingId);
        alert('Reschedule functionality coming soon!');
    }

    async reviewBooking(bookingId) {
        // This would open a review modal
        console.log('Review booking:', bookingId);
        alert('Review functionality coming soon!');
    }

    // Utility methods
    formatServiceType(serviceType) {
        return serviceType.charAt(0).toUpperCase() + serviceType.slice(1);
    }



    getStatusIcon(status) {
        const icons = {
            'pending': 'fas fa-clock',
            'confirmed': 'fas fa-check-circle',
            'in_progress': 'fas fa-play-circle',
            'completed': 'fas fa-check-double',
            'cancelled': 'fas fa-times-circle',
            'refunded': 'fas fa-undo'
        };
        return icons[status] || 'fas fa-question-circle';
    }

    formatStatus(status) {
        const statusMap = {
            'pending': 'Pending',
            'confirmed': 'Confirmed',
            'in_progress': 'In Progress',
            'completed': 'Completed',
            'cancelled': 'Cancelled',
            'refunded': 'Refunded'
        };
        return statusMap[status] || status.charAt(0).toUpperCase() + status.slice(1);
    }

    getErrorMessage(error) {
        const errorMessages = {
            'duplicate_key_value': 'A booking already exists for this time slot.',
            'foreign_key_violation': 'Invalid service or provider selected.',
            'check_violation': 'Invalid booking data provided.'
        };

        if (error.code && errorMessages[error.code]) {
            return errorMessages[error.code];
        }

        return error.message || 'An unexpected error occurred. Please try again.';
    }

    showMessage(messageId) {
        const message = document.getElementById(messageId);
        if (message) {
            message.style.display = 'block';
            setTimeout(() => {
                message.style.display = 'none';
            }, 5000);
        }
    }

    showError(errorText) {
        const errorMessage = document.getElementById('errorMessage');
        const errorTextElement = document.getElementById('errorText');
        
        if (errorMessage && errorTextElement) {
            errorTextElement.textContent = errorText;
            errorMessage.style.display = 'block';
            setTimeout(() => {
                errorMessage.style.display = 'none';
            }, 5000);
        }
    }

    showFieldError(fieldName, errorText) {
        const field = document.getElementById(fieldName);
        if (field) {
            const errorSpan = field.parentNode.querySelector('.error-message');
            if (errorSpan) {
                errorSpan.textContent = errorText;
                field.classList.add('error');
            }
        }
    }

    clearErrors() {
        const errorMessages = document.querySelectorAll('.error-message');
        errorMessages.forEach(span => span.textContent = '');
        
        const errorFields = document.querySelectorAll('.error');
        errorFields.forEach(field => field.classList.remove('error'));
        
        const messageBoxes = document.querySelectorAll('.message');
        messageBoxes.forEach(box => box.style.display = 'none');
    }

    setFormLoading(form, isLoading) {
        const submitBtn = form.querySelector('.submit-btn');
        const btnText = submitBtn.querySelector('.btn-text');
        const btnLoader = submitBtn.querySelector('.btn-loader');
        
        if (isLoading) {
            submitBtn.disabled = true;
            btnText.style.display = 'none';
            btnLoader.style.display = 'inline-block';
        } else {
            submitBtn.disabled = false;
            btnText.style.display = 'inline-block';
            btnLoader.style.display = 'none';
        }
    }
}

// Initialize booking manager when DOM is loaded
document.addEventListener('DOMContentLoaded', () => {
    window.bookingManager = new BookingManager();
});
