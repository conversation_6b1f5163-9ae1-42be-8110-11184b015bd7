// ServicePro - Enhanced Authentication Module
class AuthManager {
    constructor() {
        // Initialize Supabase client
        this.supabase = supabase.createClient(
            window.SUPABASE_CONFIG.url,
            window.SUPABASE_CONFIG.anonKey
        );

        // Current user data
        this.currentUser = null;
        this.userProfile = null;

        // Initialize
        this.init();
    }

    async init() {
        try {
            // Check for existing session
            const { data: { session }, error } = await this.supabase.auth.getSession();

            if (error) {
                console.error('Error getting session:', error);
                return;
            }

            // Set current user if session exists
            if (session?.user) {
                this.currentUser = session.user;
                await this.loadUserProfile();
            }

            // Handle auth state changes
            this.supabase.auth.onAuthStateChange(async (event, session) => {
                await this.handleAuthStateChange(event, session);
            });

            // Initialize page-specific functionality
            this.initializePage();

            console.log('✅ Auth Manager initialized successfully');
        } catch (error) {
            console.error('❌ Error initializing Auth Manager:', error);
        }
    }

    async loadUserProfile() {
        if (!this.currentUser) return null;

        try {
            const { data: profile, error } = await this.supabase
                .from('user_profiles')
                .select('*')
                .eq('id', this.currentUser.id)
                .single();

            if (error && error.code !== 'PGRST116') { // PGRST116 = no rows returned
                console.error('Error loading user profile:', error);
                return null;
            }

            this.userProfile = profile;
            return profile;
        } catch (error) {
            console.error('Error loading user profile:', error);
            return null;
        }
    }

    initializePage() {
        const currentPage = window.location.pathname.split('/').pop() || 'index.html';
        
        switch (currentPage) {
            case 'register.html':
                this.initRegisterPage();
                break;
            case 'login.html':
                this.initLoginPage();
                break;
            case 'dashboard.html':
                this.initDashboardPage();
                break;
            case 'index.html':
            case '':
                this.initIndexPage();
                break;
        }
    }

    async initIndexPage() {
        // Check if user is already logged in
        const { data: { session } } = await this.supabase.auth.getSession();
        if (session) {
            window.location.href = 'dashboard.html';
        }
    }

    initRegisterPage() {
        const form = document.getElementById('registerForm');
        if (form) {
            form.addEventListener('submit', (e) => this.handleRegister(e));
        }
    }

    initLoginPage() {
        const form = document.getElementById('loginForm');
        if (form) {
            form.addEventListener('submit', (e) => this.handleLogin(e));
        }
    }

    async initDashboardPage() {
        // Check if user is authenticated
        const { data: { session } } = await this.supabase.auth.getSession();
        if (!session) {
            window.location.href = 'login.html';
            return;
        }

        // Display user email
        const userEmailElement = document.getElementById('userEmail');
        if (userEmailElement && session.user) {
            userEmailElement.textContent = session.user.email;
        }

        // Setup logout button
        const logoutBtn = document.getElementById('logoutBtn');
        if (logoutBtn) {
            logoutBtn.addEventListener('click', () => this.handleLogout());
        }
    }

    async handleRegister(event) {
        event.preventDefault();

        const form = event.target;
        const formData = new FormData(form);
        const email = formData.get('email');
        const password = formData.get('password');
        const confirmPassword = formData.get('confirmPassword');

        // Clear previous errors
        this.clearErrors();

        // Validate form data
        if (!this.validateRegistrationForm(email, password, confirmPassword)) {
            return;
        }

        // Show loading state
        this.setFormLoading(form, true);

        try {
            // Register user with Supabase Auth
            const { data, error } = await this.supabase.auth.signUp({
                email: email,
                password: password,
                options: {
                    data: {
                        email: email,
                        email_verified: false
                    }
                }
            });

            if (error) throw error;

            // Show success message
            this.showMessage('successMessage');
            form.reset();

            console.log('✅ User registered successfully:', data.user?.email);

        } catch (error) {
            console.error('❌ Registration error:', error);
            this.showError(this.getErrorMessage(error));
        } finally {
            this.setFormLoading(form, false);
        }
    }

    validateRegistrationForm(email, password, confirmPassword) {
        let isValid = true;

        // Email validation
        if (!email || !this.isValidEmail(email)) {
            this.showFieldError('email', 'Please enter a valid email address');
            isValid = false;
        }

        // Password validation
        if (!password || password.length < 6) {
            this.showFieldError('password', 'Password must be at least 6 characters long');
            isValid = false;
        }

        // Confirm password validation
        if (password !== confirmPassword) {
            this.showFieldError('confirmPassword', 'Passwords do not match');
            isValid = false;
        }

        return isValid;
    }

    isValidEmail(email) {
        const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
        return emailRegex.test(email);
    }

    async handleLogin(event) {
        event.preventDefault();

        const form = event.target;
        const formData = new FormData(form);
        const email = formData.get('email');
        const password = formData.get('password');

        // Clear previous errors
        this.clearErrors();

        // Validate form data
        if (!this.validateLoginForm(email, password)) {
            return;
        }

        // Show loading state
        this.setFormLoading(form, true);

        try {
            // Sign in with Supabase Auth
            const { data, error } = await this.supabase.auth.signInWithPassword({
                email: email,
                password: password,
            });

            if (error) throw error;

            // Set current user
            this.currentUser = data.user;

            // Load user profile
            await this.loadUserProfile();

            // Show success message briefly, then redirect
            this.showMessage('successMessage');

            setTimeout(() => {
                window.location.href = 'dashboard.html';
            }, 1500);

            console.log('✅ User logged in successfully:', data.user?.email);

        } catch (error) {
            console.error('❌ Login error:', error);
            this.showError(this.getErrorMessage(error));
        } finally {
            this.setFormLoading(form, false);
        }
    }

    validateLoginForm(email, password) {
        let isValid = true;

        // Email validation
        if (!email || !this.isValidEmail(email)) {
            this.showFieldError('email', 'Please enter a valid email address');
            isValid = false;
        }

        // Password validation
        if (!password) {
            this.showFieldError('password', 'Please enter your password');
            isValid = false;
        }

        return isValid;
    }

    async handleLogout() {
        try {
            const { error } = await this.supabase.auth.signOut();
            if (error) throw error;
            
            window.location.href = 'index.html';
        } catch (error) {
            console.error('Logout error:', error);
            alert('Error logging out. Please try again.');
        }
    }

    async handleAuthStateChange(event, session) {
        console.log('🔄 Auth state changed:', event, session?.user?.email || 'No user');

        try {
            if (event === 'SIGNED_IN' && session?.user) {
                // User signed in
                this.currentUser = session.user;
                await this.loadUserProfile();

                const currentPage = window.location.pathname.split('/').pop();
                if (currentPage === 'login.html' || currentPage === 'register.html') {
                    window.location.href = 'dashboard.html';
                }

                console.log('✅ User signed in:', session.user.email);

            } else if (event === 'SIGNED_OUT') {
                // User signed out
                this.currentUser = null;
                this.userProfile = null;

                const currentPage = window.location.pathname.split('/').pop();
                if (currentPage === 'dashboard.html') {
                    window.location.href = 'index.html';
                }

                console.log('👋 User signed out');

            } else if (event === 'TOKEN_REFRESHED') {
                // Token refreshed
                console.log('🔄 Token refreshed');

            } else if (event === 'USER_UPDATED') {
                // User data updated
                if (session?.user) {
                    this.currentUser = session.user;
                    await this.loadUserProfile();
                }
                console.log('📝 User updated');
            }
        } catch (error) {
            console.error('❌ Error handling auth state change:', error);
        }
    }

    getErrorMessage(error) {
        // Map Supabase error codes to user-friendly messages
        const errorMessages = {
            'invalid_credentials': 'Invalid email or password. Please try again.',
            'email_not_confirmed': 'Please check your email and click the confirmation link.',
            'signup_disabled': 'New registrations are currently disabled.',
            'email_address_invalid': 'Please enter a valid email address.',
            'password_too_short': 'Password must be at least 6 characters long.',
            'user_already_registered': 'An account with this email already exists.',
            'weak_password': 'Password is too weak. Please choose a stronger password.',
            'rate_limit_exceeded': 'Too many attempts. Please wait a moment and try again.'
        };

        // Check for specific error codes
        if (error.message && errorMessages[error.message]) {
            return errorMessages[error.message];
        }

        // Check for error descriptions
        if (error.error_description) {
            return error.error_description;
        }

        // Fallback to original message
        return error.message || 'An unexpected error occurred. Please try again.';
    }

    // Utility methods
    showMessage(messageId) {
        const message = document.getElementById(messageId);
        if (message) {
            message.style.display = 'block';
            setTimeout(() => {
                message.style.display = 'none';
            }, 5000);
        }
    }

    showError(errorText) {
        const errorMessage = document.getElementById('errorMessage');
        const errorTextElement = document.getElementById('errorText');
        
        if (errorMessage && errorTextElement) {
            errorTextElement.textContent = errorText;
            errorMessage.style.display = 'block';
            setTimeout(() => {
                errorMessage.style.display = 'none';
            }, 5000);
        }
    }

    showFieldError(fieldName, errorText) {
        const field = document.getElementById(fieldName);
        if (field) {
            const errorSpan = field.parentNode.querySelector('.error-message');
            if (errorSpan) {
                errorSpan.textContent = errorText;
                field.classList.add('error');
            }
        }
    }

    clearErrors() {
        const errorMessages = document.querySelectorAll('.error-message');
        errorMessages.forEach(span => span.textContent = '');
        
        const errorFields = document.querySelectorAll('.error');
        errorFields.forEach(field => field.classList.remove('error'));
        
        const messageBoxes = document.querySelectorAll('.message');
        messageBoxes.forEach(box => box.style.display = 'none');
    }

    setFormLoading(form, isLoading) {
        const submitBtn = form.querySelector('.submit-btn');
        const btnText = submitBtn.querySelector('.btn-text');
        const btnLoader = submitBtn.querySelector('.btn-loader');
        
        if (isLoading) {
            submitBtn.disabled = true;
            btnText.style.display = 'none';
            btnLoader.style.display = 'inline-block';
        } else {
            submitBtn.disabled = false;
            btnText.style.display = 'inline-block';
            btnLoader.style.display = 'none';
        }
    }

    // User Profile Management
    async updateUserProfile(profileData) {
        if (!this.currentUser) {
            throw new Error('No authenticated user');
        }

        try {
            const { data, error } = await this.supabase
                .from('user_profiles')
                .upsert({
                    id: this.currentUser.id,
                    ...profileData,
                    updated_at: new Date().toISOString()
                })
                .select()
                .single();

            if (error) throw error;

            this.userProfile = data;
            console.log('✅ User profile updated successfully');
            return data;
        } catch (error) {
            console.error('❌ Error updating user profile:', error);
            throw error;
        }
    }

    async getUserProfile(userId = null) {
        const targetUserId = userId || this.currentUser?.id;

        if (!targetUserId) {
            return null;
        }

        try {
            const { data, error } = await this.supabase
                .from('user_profiles')
                .select('*')
                .eq('id', targetUserId)
                .single();

            if (error && error.code !== 'PGRST116') {
                throw error;
            }

            return data;
        } catch (error) {
            console.error('❌ Error getting user profile:', error);
            return null;
        }
    }

    // Authentication Status
    isAuthenticated() {
        return !!this.currentUser;
    }

    isEmailVerified() {
        return this.currentUser?.email_confirmed_at !== null;
    }

    isServiceProvider() {
        return this.userProfile?.is_service_provider === true;
    }

    // Get current user session
    async getCurrentUser() {
        if (this.currentUser) {
            return this.currentUser;
        }

        const { data: { session } } = await this.supabase.auth.getSession();
        this.currentUser = session?.user || null;

        if (this.currentUser && !this.userProfile) {
            await this.loadUserProfile();
        }

        return this.currentUser;
    }

    // Get current user profile
    getCurrentUserProfile() {
        return this.userProfile;
    }

    // Get Supabase client for use in other modules
    getSupabaseClient() {
        return this.supabase;
    }

    // Password Reset
    async resetPassword(email) {
        try {
            const { error } = await this.supabase.auth.resetPasswordForEmail(email, {
                redirectTo: `${window.location.origin}/reset-password.html`
            });

            if (error) throw error;

            console.log('✅ Password reset email sent');
            return true;
        } catch (error) {
            console.error('❌ Error sending password reset:', error);
            throw error;
        }
    }

    // Update Password
    async updatePassword(newPassword) {
        try {
            const { error } = await this.supabase.auth.updateUser({
                password: newPassword
            });

            if (error) throw error;

            console.log('✅ Password updated successfully');
            return true;
        } catch (error) {
            console.error('❌ Error updating password:', error);
            throw error;
        }
    }
}

// Initialize auth manager when DOM is loaded
document.addEventListener('DOMContentLoaded', () => {
    window.authManager = new AuthManager();
});
