// Authentication Module for Service Booking App
class AuthManager {
    constructor() {
        this.supabase = supabase.createClient(
            window.SUPABASE_CONFIG.url,
            window.SUPABASE_CONFIG.anonKey
        );
        this.init();
    }

    async init() {
        // Check for existing session
        const { data: { session } } = await this.supabase.auth.getSession();
        
        // Handle auth state changes
        this.supabase.auth.onAuthStateChange((event, session) => {
            this.handleAuthStateChange(event, session);
        });

        // Initialize page-specific functionality
        this.initializePage();
    }

    initializePage() {
        const currentPage = window.location.pathname.split('/').pop() || 'index.html';
        
        switch (currentPage) {
            case 'register.html':
                this.initRegisterPage();
                break;
            case 'login.html':
                this.initLoginPage();
                break;
            case 'dashboard.html':
                this.initDashboardPage();
                break;
            case 'index.html':
            case '':
                this.initIndexPage();
                break;
        }
    }

    async initIndexPage() {
        // Check if user is already logged in
        const { data: { session } } = await this.supabase.auth.getSession();
        if (session) {
            window.location.href = 'dashboard.html';
        }
    }

    initRegisterPage() {
        const form = document.getElementById('registerForm');
        if (form) {
            form.addEventListener('submit', (e) => this.handleRegister(e));
        }
    }

    initLoginPage() {
        const form = document.getElementById('loginForm');
        if (form) {
            form.addEventListener('submit', (e) => this.handleLogin(e));
        }
    }

    async initDashboardPage() {
        // Check if user is authenticated
        const { data: { session } } = await this.supabase.auth.getSession();
        if (!session) {
            window.location.href = 'login.html';
            return;
        }

        // Display user email
        const userEmailElement = document.getElementById('userEmail');
        if (userEmailElement && session.user) {
            userEmailElement.textContent = session.user.email;
        }

        // Setup logout button
        const logoutBtn = document.getElementById('logoutBtn');
        if (logoutBtn) {
            logoutBtn.addEventListener('click', () => this.handleLogout());
        }
    }

    async handleRegister(event) {
        event.preventDefault();
        
        const form = event.target;
        const formData = new FormData(form);
        const email = formData.get('email');
        const password = formData.get('password');
        const confirmPassword = formData.get('confirmPassword');

        // Clear previous errors
        this.clearErrors();

        // Validate passwords match
        if (password !== confirmPassword) {
            this.showFieldError('confirmPassword', 'Passwords do not match');
            return;
        }

        // Show loading state
        this.setFormLoading(form, true);

        try {
            const { data, error } = await this.supabase.auth.signUp({
                email: email,
                password: password,
            });

            if (error) throw error;

            // Show success message
            this.showMessage('successMessage');
            form.reset();

        } catch (error) {
            console.error('Registration error:', error);
            this.showError(error.message);
        } finally {
            this.setFormLoading(form, false);
        }
    }

    async handleLogin(event) {
        event.preventDefault();
        
        const form = event.target;
        const formData = new FormData(form);
        const email = formData.get('email');
        const password = formData.get('password');

        // Clear previous errors
        this.clearErrors();

        // Show loading state
        this.setFormLoading(form, true);

        try {
            const { data, error } = await this.supabase.auth.signInWithPassword({
                email: email,
                password: password,
            });

            if (error) throw error;

            // Show success message briefly, then redirect
            this.showMessage('successMessage');
            setTimeout(() => {
                window.location.href = 'dashboard.html';
            }, 1500);

        } catch (error) {
            console.error('Login error:', error);
            this.showError(error.message);
        } finally {
            this.setFormLoading(form, false);
        }
    }

    async handleLogout() {
        try {
            const { error } = await this.supabase.auth.signOut();
            if (error) throw error;
            
            window.location.href = 'index.html';
        } catch (error) {
            console.error('Logout error:', error);
            alert('Error logging out. Please try again.');
        }
    }

    handleAuthStateChange(event, session) {
        console.log('Auth state changed:', event, session);
        
        if (event === 'SIGNED_IN') {
            // User signed in
            const currentPage = window.location.pathname.split('/').pop();
            if (currentPage === 'login.html' || currentPage === 'register.html') {
                window.location.href = 'dashboard.html';
            }
        } else if (event === 'SIGNED_OUT') {
            // User signed out
            const currentPage = window.location.pathname.split('/').pop();
            if (currentPage === 'dashboard.html') {
                window.location.href = 'index.html';
            }
        }
    }

    // Utility methods
    showMessage(messageId) {
        const message = document.getElementById(messageId);
        if (message) {
            message.style.display = 'block';
            setTimeout(() => {
                message.style.display = 'none';
            }, 5000);
        }
    }

    showError(errorText) {
        const errorMessage = document.getElementById('errorMessage');
        const errorTextElement = document.getElementById('errorText');
        
        if (errorMessage && errorTextElement) {
            errorTextElement.textContent = errorText;
            errorMessage.style.display = 'block';
            setTimeout(() => {
                errorMessage.style.display = 'none';
            }, 5000);
        }
    }

    showFieldError(fieldName, errorText) {
        const field = document.getElementById(fieldName);
        if (field) {
            const errorSpan = field.parentNode.querySelector('.error-message');
            if (errorSpan) {
                errorSpan.textContent = errorText;
                field.classList.add('error');
            }
        }
    }

    clearErrors() {
        const errorMessages = document.querySelectorAll('.error-message');
        errorMessages.forEach(span => span.textContent = '');
        
        const errorFields = document.querySelectorAll('.error');
        errorFields.forEach(field => field.classList.remove('error'));
        
        const messageBoxes = document.querySelectorAll('.message');
        messageBoxes.forEach(box => box.style.display = 'none');
    }

    setFormLoading(form, isLoading) {
        const submitBtn = form.querySelector('.submit-btn');
        const btnText = submitBtn.querySelector('.btn-text');
        const btnLoader = submitBtn.querySelector('.btn-loader');
        
        if (isLoading) {
            submitBtn.disabled = true;
            btnText.style.display = 'none';
            btnLoader.style.display = 'inline-block';
        } else {
            submitBtn.disabled = false;
            btnText.style.display = 'inline-block';
            btnLoader.style.display = 'none';
        }
    }

    // Get current user session
    async getCurrentUser() {
        const { data: { session } } = await this.supabase.auth.getSession();
        return session?.user || null;
    }

    // Get Supabase client for use in other modules
    getSupabaseClient() {
        return this.supabase;
    }
}

// Initialize auth manager when DOM is loaded
document.addEventListener('DOMContentLoaded', () => {
    window.authManager = new AuthManager();
});
