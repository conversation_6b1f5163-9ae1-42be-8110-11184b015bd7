<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Form Submissions - Admin Dashboard</title>
    <link rel="stylesheet" href="submissions.css">
    <link href="https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700&display=swap" rel="stylesheet">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css">
</head>
<body>
    <div class="container">
        <header class="dashboard-header">
            <div class="header-content">
                <h1><i class="fas fa-chart-line"></i> Form Submissions Dashboard</h1>
                <div class="header-actions">
                    <a href="index.html" class="btn btn-secondary">
                        <i class="fas fa-arrow-left"></i> Back to Form
                    </a>
                    <button id="refreshBtn" class="btn btn-primary">
                        <i class="fas fa-sync-alt"></i> Refresh
                    </button>
                </div>
            </div>
        </header>

        <div class="dashboard-content">
            <!-- Stats Cards -->
            <div class="stats-grid">
                <div class="stat-card">
                    <div class="stat-icon">
                        <i class="fas fa-envelope"></i>
                    </div>
                    <div class="stat-content">
                        <h3 id="totalSubmissions">0</h3>
                        <p>Total Submissions</p>
                    </div>
                </div>
                <div class="stat-card">
                    <div class="stat-icon">
                        <i class="fas fa-calendar-day"></i>
                    </div>
                    <div class="stat-content">
                        <h3 id="todaySubmissions">0</h3>
                        <p>Today's Submissions</p>
                    </div>
                </div>
                <div class="stat-card">
                    <div class="stat-icon">
                        <i class="fas fa-users"></i>
                    </div>
                    <div class="stat-content">
                        <h3 id="newsletterSubscribers">0</h3>
                        <p>Newsletter Subscribers</p>
                    </div>
                </div>
                <div class="stat-card">
                    <div class="stat-icon">
                        <i class="fas fa-star"></i>
                    </div>
                    <div class="stat-content">
                        <h3 id="popularSubject">-</h3>
                        <p>Most Popular Subject</p>
                    </div>
                </div>
            </div>

            <!-- Search and Filter Controls -->
            <div class="controls-section">
                <div class="search-controls">
                    <div class="search-box">
                        <i class="fas fa-search"></i>
                        <input type="text" id="searchInput" placeholder="Search by name, email, company, or message...">
                        <button id="clearSearch" class="clear-btn">
                            <i class="fas fa-times"></i>
                        </button>
                    </div>
                    
                    <div class="filter-controls">
                        <select id="subjectFilter">
                            <option value="">All Subjects</option>
                            <option value="general">General Inquiry</option>
                            <option value="support">Technical Support</option>
                            <option value="sales">Sales Question</option>
                            <option value="partnership">Partnership</option>
                            <option value="feedback">Feedback</option>
                            <option value="other">Other</option>
                        </select>
                        
                        <select id="newsletterFilter">
                            <option value="">All Newsletter Status</option>
                            <option value="true">Subscribed</option>
                            <option value="false">Not Subscribed</option>
                        </select>
                        
                        <input type="date" id="dateFilter" title="Filter by date">
                        
                        <button id="exportBtn" class="btn btn-outline">
                            <i class="fas fa-download"></i> Export CSV
                        </button>
                    </div>
                </div>
            </div>

            <!-- Submissions Table -->
            <div class="table-section">
                <div class="table-header">
                    <h2>Submissions</h2>
                    <div class="table-info">
                        <span id="resultsCount">Loading...</span>
                    </div>
                </div>
                
                <div class="table-container">
                    <table id="submissionsTable" class="submissions-table">
                        <thead>
                            <tr>
                                <th data-sort="created_at">
                                    Date <i class="fas fa-sort"></i>
                                </th>
                                <th data-sort="first_name">
                                    Name <i class="fas fa-sort"></i>
                                </th>
                                <th data-sort="email">
                                    Email <i class="fas fa-sort"></i>
                                </th>
                                <th data-sort="subject">
                                    Subject <i class="fas fa-sort"></i>
                                </th>
                                <th data-sort="company">
                                    Company <i class="fas fa-sort"></i>
                                </th>
                                <th>Newsletter</th>
                                <th>Actions</th>
                            </tr>
                        </thead>
                        <tbody id="submissionsTableBody">
                            <!-- Dynamic content will be inserted here -->
                        </tbody>
                    </table>
                </div>
                
                <div class="table-footer">
                    <div class="pagination">
                        <button id="prevPage" class="btn btn-outline" disabled>
                            <i class="fas fa-chevron-left"></i> Previous
                        </button>
                        <span id="pageInfo">Page 1 of 1</span>
                        <button id="nextPage" class="btn btn-outline" disabled>
                            Next <i class="fas fa-chevron-right"></i>
                        </button>
                    </div>
                </div>
            </div>
        </div>

        <!-- Loading Overlay -->
        <div id="loadingOverlay" class="loading-overlay">
            <div class="loading-spinner">
                <i class="fas fa-spinner fa-spin"></i>
                <p>Loading submissions...</p>
            </div>
        </div>

        <!-- Submission Detail Modal -->
        <div id="submissionModal" class="modal">
            <div class="modal-content">
                <div class="modal-header">
                    <h3>Submission Details</h3>
                    <button class="modal-close">
                        <i class="fas fa-times"></i>
                    </button>
                </div>
                <div class="modal-body" id="modalBody">
                    <!-- Dynamic content will be inserted here -->
                </div>
                <div class="modal-footer">
                    <button class="btn btn-secondary modal-close">Close</button>
                    <button id="replyBtn" class="btn btn-primary">
                        <i class="fas fa-reply"></i> Reply via Email
                    </button>
                </div>
            </div>
        </div>
    </div>

    <script src="https://cdn.jsdelivr.net/npm/@supabase/supabase-js@2"></script>
    <script src="config.js"></script>
    <script src="submissions.js"></script>
</body>
</html>
