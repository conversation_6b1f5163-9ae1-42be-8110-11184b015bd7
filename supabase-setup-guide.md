# ServicePro - Supabase Setup Guide

## 🚀 Complete Setup Instructions

### 1. Create Supabase Project

1. Go to [supabase.com](https://supabase.com)
2. Click "Start your project"
3. Sign up/Login with GitHub
4. Click "New Project"
5. Choose your organization
6. Fill in project details:
   - **Name**: ServicePro
   - **Database Password**: Create a strong password (save it!)
   - **Region**: Choose closest to your users
7. Click "Create new project"
8. Wait for project to be ready (2-3 minutes)

### 2. Get Your Project Credentials

1. Go to **Settings** → **API**
2. Copy these values:
   - **Project URL** (looks like: `https://xxxxx.supabase.co`)
   - **Project API Key** (anon public key)
3. Update your `config.js` file with these values

### 3. Configure Authentication

1. Go to **Authentication** → **Settings**
2. **Site URL**: Set to your domain (for development: `http://localhost:3000` or your local server)
3. **Redirect URLs**: Add your domain URLs
4. **Email Templates**: Customize if needed
5. **Auth Providers**: 
   - Email is enabled by default
   - Enable others if needed (Google, GitHub, etc.)

### 4. Set Up Database Schema

1. Go to **SQL Editor**
2. Copy the entire content from `database-setup.sql`
3. Paste it in the SQL Editor
4. Click "Run" to execute
5. Verify all tables are created in **Database** → **Tables**

### 5. Configure Row Level Security (RLS)

The SQL script automatically sets up RLS policies, but verify:

1. Go to **Authentication** → **Policies**
2. Check that policies exist for all tables
3. Test policies work correctly

### 6. Set Up Storage (Optional)

For user avatars and file uploads:

1. Go to **Storage**
2. Create a new bucket: `avatars`
3. Set bucket to **Public** if needed
4. Configure upload policies

### 7. Environment Variables

Update your `config.js` with your actual Supabase credentials:

```javascript
window.SUPABASE_CONFIG = {
    url: 'YOUR_SUPABASE_URL',
    anonKey: 'YOUR_SUPABASE_ANON_KEY'
};
```

## 🔐 Authentication Features Enabled

### Email/Password Authentication
- User registration with email verification
- Secure login/logout
- Password reset functionality
- Email confirmation

### User Profile Management
- Automatic profile creation on signup
- Extended user information storage
- Profile picture support
- Address and contact information

### Security Features
- Row Level Security (RLS) on all tables
- JWT token-based authentication
- Secure API access
- Data isolation between users

## 📊 Database Schema Overview

### Core Tables Created:

1. **user_profiles** - Extended user information
2. **service_categories** - Service organization
3. **services** - Available services
4. **service_providers** - Provider profiles
5. **provider_services** - Provider-service relationships
6. **bookings** - Service bookings
7. **reviews** - Customer reviews
8. **provider_availability** - Provider schedules
9. **provider_blocked_dates** - Unavailable dates

### Key Features:
- ✅ Automatic user profile creation
- ✅ Provider rating system
- ✅ Booking status tracking
- ✅ Review and rating system
- ✅ Availability management
- ✅ Location-based services
- ✅ Pricing flexibility

## 🧪 Testing Your Setup

### 1. Test Authentication
1. Try registering a new user
2. Check email for verification
3. Test login/logout
4. Verify user profile is created

### 2. Test Database
1. Check that tables exist
2. Verify RLS policies work
3. Test sample data insertion

### 3. Test Booking Flow
1. Create a test booking
2. Verify data is saved correctly
3. Test booking status updates

## 🔧 Troubleshooting

### Common Issues:

**Authentication not working:**
- Check Site URL in Auth settings
- Verify API keys are correct
- Check browser console for errors

**Database errors:**
- Verify SQL script ran completely
- Check RLS policies are enabled
- Ensure user has proper permissions

**CORS errors:**
- Add your domain to allowed origins
- Check Site URL configuration

## 📝 Next Steps

1. ✅ Database schema created
2. ✅ Authentication configured
3. 🔄 Update frontend code
4. 🔄 Test booking functionality
5. 🔄 Deploy to production

## 🆘 Support

If you encounter issues:
1. Check Supabase documentation
2. Review browser console errors
3. Check Supabase project logs
4. Verify all configuration steps

---

**Your ServicePro database is now ready for production! 🎉**
