// Supabase client initialization
let supabaseClient;

// Global variables
let allSubmissions = [];
let filteredSubmissions = [];
let currentPage = 1;
let itemsPerPage = 10;
let currentSort = { field: 'created_at', direction: 'desc' };

// DOM elements
const loadingOverlay = document.getElementById('loadingOverlay');
const searchInput = document.getElementById('searchInput');
const clearSearchBtn = document.getElementById('clearSearch');
const subjectFilter = document.getElementById('subjectFilter');
const newsletterFilter = document.getElementById('newsletterFilter');
const dateFilter = document.getElementById('dateFilter');
const refreshBtn = document.getElementById('refreshBtn');
const exportBtn = document.getElementById('exportBtn');
const submissionsTableBody = document.getElementById('submissionsTableBody');
const resultsCount = document.getElementById('resultsCount');
const prevPageBtn = document.getElementById('prevPage');
const nextPageBtn = document.getElementById('nextPage');
const pageInfo = document.getElementById('pageInfo');
const submissionModal = document.getElementById('submissionModal');
const modalBody = document.getElementById('modalBody');
const replyBtn = document.getElementById('replyBtn');

// Initialize the application
document.addEventListener('DOMContentLoaded', function() {
    if (initializeSupabase()) {
        setupEventListeners();
        loadSubmissions();
    }
});

// Initialize Supabase client
function initializeSupabase() {
    if (window.SUPABASE_CONFIG) {
        supabaseClient = supabase.createClient(
            window.SUPABASE_CONFIG.url, 
            window.SUPABASE_CONFIG.anonKey
        );
        return true;
    } else {
        showError('Supabase configuration not found. Please check your config.js file.');
        return false;
    }
}

// Setup event listeners
function setupEventListeners() {
    // Search and filters
    searchInput.addEventListener('input', debounce(handleSearch, 300));
    clearSearchBtn.addEventListener('click', clearSearch);
    subjectFilter.addEventListener('change', applyFilters);
    newsletterFilter.addEventListener('change', applyFilters);
    dateFilter.addEventListener('change', applyFilters);
    
    // Actions
    refreshBtn.addEventListener('click', loadSubmissions);
    exportBtn.addEventListener('click', exportToCSV);
    
    // Pagination
    prevPageBtn.addEventListener('click', () => changePage(currentPage - 1));
    nextPageBtn.addEventListener('click', () => changePage(currentPage + 1));
    
    // Table sorting
    document.querySelectorAll('[data-sort]').forEach(header => {
        header.addEventListener('click', () => handleSort(header.dataset.sort));
    });
    
    // Modal
    document.querySelectorAll('.modal-close').forEach(btn => {
        btn.addEventListener('click', closeModal);
    });
    
    submissionModal.addEventListener('click', (e) => {
        if (e.target === submissionModal) closeModal();
    });
    
    replyBtn.addEventListener('click', handleReply);
}

// Load submissions from Supabase
async function loadSubmissions() {
    showLoading(true);
    
    try {
        const { data, error } = await supabaseClient
            .from('contact_submissions')
            .select('*')
            .order('created_at', { ascending: false });
        
        if (error) {
            throw error;
        }
        
        allSubmissions = data || [];
        updateStats();
        applyFilters();
        
    } catch (error) {
        console.error('Error loading submissions:', error);
        showError('Failed to load submissions. Please try again.');
    } finally {
        showLoading(false);
    }
}

// Update statistics
function updateStats() {
    const total = allSubmissions.length;
    const today = new Date().toDateString();
    const todayCount = allSubmissions.filter(sub => 
        new Date(sub.created_at).toDateString() === today
    ).length;
    const newsletterCount = allSubmissions.filter(sub => sub.newsletter_subscription).length;
    
    // Find most popular subject
    const subjectCounts = {};
    allSubmissions.forEach(sub => {
        subjectCounts[sub.subject] = (subjectCounts[sub.subject] || 0) + 1;
    });
    const popularSubject = Object.keys(subjectCounts).reduce((a, b) => 
        subjectCounts[a] > subjectCounts[b] ? a : b, '-'
    );
    
    document.getElementById('totalSubmissions').textContent = total;
    document.getElementById('todaySubmissions').textContent = todayCount;
    document.getElementById('newsletterSubscribers').textContent = newsletterCount;
    document.getElementById('popularSubject').textContent = 
        popularSubject === '-' ? '-' : popularSubject.charAt(0).toUpperCase() + popularSubject.slice(1);
}

// Handle search
function handleSearch() {
    applyFilters();
}

// Clear search
function clearSearch() {
    searchInput.value = '';
    applyFilters();
}

// Apply filters and search
function applyFilters() {
    const searchTerm = searchInput.value.toLowerCase().trim();
    const subjectValue = subjectFilter.value;
    const newsletterValue = newsletterFilter.value;
    const dateValue = dateFilter.value;
    
    filteredSubmissions = allSubmissions.filter(submission => {
        // Search filter
        const searchMatch = !searchTerm || 
            submission.first_name.toLowerCase().includes(searchTerm) ||
            submission.last_name.toLowerCase().includes(searchTerm) ||
            submission.email.toLowerCase().includes(searchTerm) ||
            (submission.company && submission.company.toLowerCase().includes(searchTerm)) ||
            submission.message.toLowerCase().includes(searchTerm);
        
        // Subject filter
        const subjectMatch = !subjectValue || submission.subject === subjectValue;
        
        // Newsletter filter
        const newsletterMatch = !newsletterValue || 
            submission.newsletter_subscription.toString() === newsletterValue;
        
        // Date filter
        const dateMatch = !dateValue || 
            new Date(submission.created_at).toDateString() === new Date(dateValue).toDateString();
        
        return searchMatch && subjectMatch && newsletterMatch && dateMatch;
    });
    
    // Apply sorting
    applySorting();
    
    // Reset to first page
    currentPage = 1;
    renderTable();
    updatePagination();
}

// Apply sorting
function applySorting() {
    filteredSubmissions.sort((a, b) => {
        let aValue = a[currentSort.field];
        let bValue = b[currentSort.field];
        
        // Handle null values
        if (aValue === null) aValue = '';
        if (bValue === null) bValue = '';
        
        // Convert to lowercase for string comparison
        if (typeof aValue === 'string') aValue = aValue.toLowerCase();
        if (typeof bValue === 'string') bValue = bValue.toLowerCase();
        
        if (currentSort.direction === 'asc') {
            return aValue > bValue ? 1 : -1;
        } else {
            return aValue < bValue ? 1 : -1;
        }
    });
}

// Handle table sorting
function handleSort(field) {
    if (currentSort.field === field) {
        currentSort.direction = currentSort.direction === 'asc' ? 'desc' : 'asc';
    } else {
        currentSort.field = field;
        currentSort.direction = 'asc';
    }
    
    // Update sort icons
    document.querySelectorAll('[data-sort] i').forEach(icon => {
        icon.className = 'fas fa-sort';
    });
    
    const currentHeader = document.querySelector(`[data-sort="${field}"] i`);
    currentHeader.className = currentSort.direction === 'asc' ? 'fas fa-sort-up' : 'fas fa-sort-down';
    
    applySorting();
    renderTable();
}

// Render table
function renderTable() {
    const startIndex = (currentPage - 1) * itemsPerPage;
    const endIndex = startIndex + itemsPerPage;
    const pageSubmissions = filteredSubmissions.slice(startIndex, endIndex);
    
    if (pageSubmissions.length === 0) {
        submissionsTableBody.innerHTML = `
            <tr>
                <td colspan="7" class="empty-state">
                    <i class="fas fa-inbox"></i>
                    <h3>No submissions found</h3>
                    <p>Try adjusting your search or filter criteria.</p>
                </td>
            </tr>
        `;
    } else {
        submissionsTableBody.innerHTML = pageSubmissions.map(submission => `
            <tr>
                <td>${formatDate(submission.created_at)}</td>
                <td class="name-cell">${submission.first_name} ${submission.last_name}</td>
                <td class="email-cell">${submission.email}</td>
                <td>
                    <span class="subject-badge subject-${submission.subject}">
                        ${submission.subject.charAt(0).toUpperCase() + submission.subject.slice(1)}
                    </span>
                </td>
                <td>${submission.company || '-'}</td>
                <td>
                    <span class="newsletter-badge newsletter-${submission.newsletter_subscription ? 'yes' : 'no'}">
                        <i class="fas fa-${submission.newsletter_subscription ? 'check' : 'times'}"></i>
                        ${submission.newsletter_subscription ? 'Yes' : 'No'}
                    </span>
                </td>
                <td>
                    <div class="action-buttons">
                        <button class="action-btn view-btn" onclick="viewSubmission(${submission.id})">
                            <i class="fas fa-eye"></i> View
                        </button>
                    </div>
                </td>
            </tr>
        `).join('');
    }
    
    // Update results count
    resultsCount.textContent = `Showing ${pageSubmissions.length} of ${filteredSubmissions.length} submissions`;
}

// Update pagination
function updatePagination() {
    const totalPages = Math.ceil(filteredSubmissions.length / itemsPerPage);
    
    prevPageBtn.disabled = currentPage <= 1;
    nextPageBtn.disabled = currentPage >= totalPages;
    
    pageInfo.textContent = `Page ${currentPage} of ${totalPages}`;
}

// Change page
function changePage(page) {
    const totalPages = Math.ceil(filteredSubmissions.length / itemsPerPage);
    
    if (page >= 1 && page <= totalPages) {
        currentPage = page;
        renderTable();
        updatePagination();
    }
}

// View submission details
function viewSubmission(id) {
    const submission = allSubmissions.find(sub => sub.id === id);
    if (!submission) return;
    
    modalBody.innerHTML = `
        <div class="detail-group">
            <span class="detail-label">Name:</span>
            <div class="detail-value">${submission.first_name} ${submission.last_name}</div>
        </div>
        <div class="detail-group">
            <span class="detail-label">Email:</span>
            <div class="detail-value">${submission.email}</div>
        </div>
        ${submission.phone ? `
        <div class="detail-group">
            <span class="detail-label">Phone:</span>
            <div class="detail-value">${submission.phone}</div>
        </div>
        ` : ''}
        ${submission.company ? `
        <div class="detail-group">
            <span class="detail-label">Company:</span>
            <div class="detail-value">${submission.company}</div>
        </div>
        ` : ''}
        <div class="detail-group">
            <span class="detail-label">Subject:</span>
            <div class="detail-value">
                <span class="subject-badge subject-${submission.subject}">
                    ${submission.subject.charAt(0).toUpperCase() + submission.subject.slice(1)}
                </span>
            </div>
        </div>
        <div class="detail-group">
            <span class="detail-label">Newsletter Subscription:</span>
            <div class="detail-value">
                <span class="newsletter-badge newsletter-${submission.newsletter_subscription ? 'yes' : 'no'}">
                    <i class="fas fa-${submission.newsletter_subscription ? 'check' : 'times'}"></i>
                    ${submission.newsletter_subscription ? 'Yes' : 'No'}
                </span>
            </div>
        </div>
        <div class="detail-group">
            <span class="detail-label">Submitted:</span>
            <div class="detail-value">${formatDateTime(submission.created_at)}</div>
        </div>
        <div class="detail-group">
            <span class="detail-label">Message:</span>
            <div class="detail-value">
                <div class="message-content">${submission.message}</div>
            </div>
        </div>
    `;
    
    // Store current submission for reply functionality
    replyBtn.dataset.email = submission.email;
    replyBtn.dataset.name = `${submission.first_name} ${submission.last_name}`;
    replyBtn.dataset.subject = submission.subject;
    
    submissionModal.classList.add('active');
}

// Close modal
function closeModal() {
    submissionModal.classList.remove('active');
}

// Handle reply
function handleReply() {
    const email = replyBtn.dataset.email;
    const name = replyBtn.dataset.name;
    const subject = replyBtn.dataset.subject;
    
    const mailtoLink = `mailto:${email}?subject=Re: ${subject}&body=Hi ${name},%0D%0A%0D%0AThank you for contacting us.%0D%0A%0D%0ABest regards`;
    window.open(mailtoLink);
}

// Export to CSV
function exportToCSV() {
    const headers = ['Date', 'First Name', 'Last Name', 'Email', 'Phone', 'Company', 'Subject', 'Message', 'Newsletter'];
    const csvContent = [
        headers.join(','),
        ...filteredSubmissions.map(sub => [
            formatDate(sub.created_at),
            `"${sub.first_name}"`,
            `"${sub.last_name}"`,
            `"${sub.email}"`,
            `"${sub.phone || ''}"`,
            `"${sub.company || ''}"`,
            `"${sub.subject}"`,
            `"${sub.message.replace(/"/g, '""')}"`,
            sub.newsletter_subscription ? 'Yes' : 'No'
        ].join(','))
    ].join('\n');
    
    const blob = new Blob([csvContent], { type: 'text/csv' });
    const url = window.URL.createObjectURL(blob);
    const a = document.createElement('a');
    a.href = url;
    a.download = `contact-submissions-${new Date().toISOString().split('T')[0]}.csv`;
    a.click();
    window.URL.revokeObjectURL(url);
}

// Utility functions
function formatDate(dateString) {
    return new Date(dateString).toLocaleDateString();
}

function formatDateTime(dateString) {
    return new Date(dateString).toLocaleString();
}

function showLoading(show) {
    loadingOverlay.style.display = show ? 'flex' : 'none';
}

function showError(message) {
    alert(message); // You can replace this with a better notification system
}

function debounce(func, wait) {
    let timeout;
    return function executedFunction(...args) {
        const later = () => {
            clearTimeout(timeout);
            func(...args);
        };
        clearTimeout(timeout);
        timeout = setTimeout(later, wait);
    };
}
