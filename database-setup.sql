-- ServicePro - Comprehensive Database Setup for Supabase
-- Run this SQL in your Supabase SQL Editor

-- =====================================================
-- 1. USER PROFILES TABLE
-- =====================================================
-- Create user profiles table to extend auth.users
CREATE TABLE IF NOT EXISTS user_profiles (
    id UUID REFERENCES auth.users(id) ON DELETE CASCADE PRIMARY KEY,
    email VARCHAR(255) NOT NULL,
    first_name VARCHAR(100),
    last_name VARCHAR(100),
    phone VARCHAR(20),
    avatar_url TEXT,
    date_of_birth DATE,
    address TEXT,
    city VARCHAR(100),
    state VARCHAR(100),
    postal_code VARCHAR(20),
    country VARCHAR(100) DEFAULT 'United States',
    is_service_provider BOOLEAN DEFAULT FALSE,
    email_verified BOOLEAN DEFAULT FALSE,
    phone_verified BOOLEAN DEFAULT FALSE,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- =====================================================
-- 2. SERVICE CATEGORIES TABLE
-- =====================================================
CREATE TABLE IF NOT EXISTS service_categories (
    id UUID DEFAULT gen_random_uuid() PRIMARY KEY,
    name VARCHAR(100) NOT NULL UNIQUE,
    description TEXT,
    icon VARCHAR(50),
    color VARCHAR(7), -- Hex color code
    is_active BOOLEAN DEFAULT TRUE,
    sort_order INTEGER DEFAULT 0,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- =====================================================
-- 3. SERVICES TABLE
-- =====================================================
CREATE TABLE IF NOT EXISTS services (
    id UUID DEFAULT gen_random_uuid() PRIMARY KEY,
    category_id UUID REFERENCES service_categories(id) ON DELETE SET NULL,
    name VARCHAR(200) NOT NULL,
    description TEXT,
    short_description VARCHAR(500),
    price_type VARCHAR(20) DEFAULT 'hourly' CHECK (price_type IN ('hourly', 'fixed', 'custom')),
    base_price DECIMAL(10,2),
    duration_minutes INTEGER, -- Default duration in minutes
    is_active BOOLEAN DEFAULT TRUE,
    requires_approval BOOLEAN DEFAULT FALSE,
    max_advance_booking_days INTEGER DEFAULT 30,
    min_advance_booking_hours INTEGER DEFAULT 2,
    cancellation_policy TEXT,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- =====================================================
-- 4. SERVICE PROVIDERS TABLE
-- =====================================================
CREATE TABLE IF NOT EXISTS service_providers (
    id UUID DEFAULT gen_random_uuid() PRIMARY KEY,
    user_id UUID REFERENCES auth.users(id) ON DELETE CASCADE NOT NULL,
    business_name VARCHAR(200),
    business_description TEXT,
    business_license VARCHAR(100),
    insurance_info TEXT,
    years_experience INTEGER,
    service_area_radius INTEGER DEFAULT 25, -- in miles/km
    is_verified BOOLEAN DEFAULT FALSE,
    is_active BOOLEAN DEFAULT TRUE,
    rating_average DECIMAL(3,2) DEFAULT 0.00,
    rating_count INTEGER DEFAULT 0,
    total_bookings INTEGER DEFAULT 0,
    response_time_hours INTEGER DEFAULT 24,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- =====================================================
-- 5. PROVIDER SERVICES (Many-to-Many)
-- =====================================================
CREATE TABLE IF NOT EXISTS provider_services (
    id UUID DEFAULT gen_random_uuid() PRIMARY KEY,
    provider_id UUID REFERENCES service_providers(id) ON DELETE CASCADE NOT NULL,
    service_id UUID REFERENCES services(id) ON DELETE CASCADE NOT NULL,
    custom_price DECIMAL(10,2), -- Provider can override base price
    custom_duration_minutes INTEGER, -- Provider can override duration
    is_available BOOLEAN DEFAULT TRUE,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    UNIQUE(provider_id, service_id)
);

-- =====================================================
-- 6. BOOKINGS TABLE (Enhanced)
-- =====================================================
CREATE TABLE IF NOT EXISTS bookings (
    id UUID DEFAULT gen_random_uuid() PRIMARY KEY,
    user_id UUID REFERENCES auth.users(id) ON DELETE CASCADE NOT NULL,
    provider_id UUID REFERENCES service_providers(id) ON DELETE SET NULL,
    service_id UUID REFERENCES services(id) ON DELETE SET NULL,

    -- Booking Details
    booking_date DATE NOT NULL,
    booking_time TIME NOT NULL,
    duration_minutes INTEGER NOT NULL DEFAULT 60,

    -- Pricing
    quoted_price DECIMAL(10,2),
    final_price DECIMAL(10,2),
    currency VARCHAR(3) DEFAULT 'USD',

    -- Status and Notes
    status VARCHAR(20) DEFAULT 'pending' CHECK (status IN ('pending', 'confirmed', 'in_progress', 'completed', 'cancelled', 'refunded')),
    customer_notes TEXT,
    provider_notes TEXT,
    cancellation_reason TEXT,

    -- Location
    service_address TEXT,
    service_city VARCHAR(100),
    service_state VARCHAR(100),
    service_postal_code VARCHAR(20),
    latitude DECIMAL(10, 8),
    longitude DECIMAL(11, 8),

    -- Timestamps
    confirmed_at TIMESTAMP WITH TIME ZONE,
    started_at TIMESTAMP WITH TIME ZONE,
    completed_at TIMESTAMP WITH TIME ZONE,
    cancelled_at TIMESTAMP WITH TIME ZONE,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- =====================================================
-- 7. REVIEWS AND RATINGS TABLE
-- =====================================================
CREATE TABLE IF NOT EXISTS reviews (
    id UUID DEFAULT gen_random_uuid() PRIMARY KEY,
    booking_id UUID REFERENCES bookings(id) ON DELETE CASCADE NOT NULL,
    reviewer_id UUID REFERENCES auth.users(id) ON DELETE CASCADE NOT NULL,
    provider_id UUID REFERENCES service_providers(id) ON DELETE CASCADE NOT NULL,

    -- Rating (1-5 stars)
    rating INTEGER NOT NULL CHECK (rating >= 1 AND rating <= 5),

    -- Review Content
    title VARCHAR(200),
    comment TEXT,

    -- Review Categories
    quality_rating INTEGER CHECK (quality_rating >= 1 AND quality_rating <= 5),
    punctuality_rating INTEGER CHECK (punctuality_rating >= 1 AND punctuality_rating <= 5),
    communication_rating INTEGER CHECK (communication_rating >= 1 AND communication_rating <= 5),
    value_rating INTEGER CHECK (value_rating >= 1 AND value_rating <= 5),

    -- Moderation
    is_verified BOOLEAN DEFAULT FALSE,
    is_featured BOOLEAN DEFAULT FALSE,
    is_hidden BOOLEAN DEFAULT FALSE,

    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),

    UNIQUE(booking_id, reviewer_id)
);

-- =====================================================
-- 8. AVAILABILITY SCHEDULE TABLE
-- =====================================================
CREATE TABLE IF NOT EXISTS provider_availability (
    id UUID DEFAULT gen_random_uuid() PRIMARY KEY,
    provider_id UUID REFERENCES service_providers(id) ON DELETE CASCADE NOT NULL,
    day_of_week INTEGER NOT NULL CHECK (day_of_week >= 0 AND day_of_week <= 6), -- 0 = Sunday
    start_time TIME NOT NULL,
    end_time TIME NOT NULL,
    is_available BOOLEAN DEFAULT TRUE,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),

    UNIQUE(provider_id, day_of_week, start_time)
);

-- =====================================================
-- 9. BLOCKED DATES TABLE
-- =====================================================
CREATE TABLE IF NOT EXISTS provider_blocked_dates (
    id UUID DEFAULT gen_random_uuid() PRIMARY KEY,
    provider_id UUID REFERENCES service_providers(id) ON DELETE CASCADE NOT NULL,
    blocked_date DATE NOT NULL,
    start_time TIME,
    end_time TIME,
    reason VARCHAR(200),
    is_recurring BOOLEAN DEFAULT FALSE,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),

    UNIQUE(provider_id, blocked_date, start_time)
);

-- =====================================================
-- 10. INDEXES FOR PERFORMANCE
-- =====================================================

-- User Profiles Indexes
CREATE INDEX IF NOT EXISTS idx_user_profiles_email ON user_profiles(email);
CREATE INDEX IF NOT EXISTS idx_user_profiles_location ON user_profiles(city, state, country);
CREATE INDEX IF NOT EXISTS idx_user_profiles_provider ON user_profiles(is_service_provider);

-- Service Categories Indexes
CREATE INDEX IF NOT EXISTS idx_service_categories_active ON service_categories(is_active, sort_order);

-- Services Indexes
CREATE INDEX IF NOT EXISTS idx_services_category ON services(category_id);
CREATE INDEX IF NOT EXISTS idx_services_active ON services(is_active);
CREATE INDEX IF NOT EXISTS idx_services_price ON services(price_type, base_price);

-- Service Providers Indexes
CREATE INDEX IF NOT EXISTS idx_providers_user ON service_providers(user_id);
CREATE INDEX IF NOT EXISTS idx_providers_active ON service_providers(is_active, is_verified);
CREATE INDEX IF NOT EXISTS idx_providers_rating ON service_providers(rating_average DESC);
CREATE INDEX IF NOT EXISTS idx_providers_location ON service_providers(service_area_radius);

-- Provider Services Indexes
CREATE INDEX IF NOT EXISTS idx_provider_services_provider ON provider_services(provider_id);
CREATE INDEX IF NOT EXISTS idx_provider_services_service ON provider_services(service_id);
CREATE INDEX IF NOT EXISTS idx_provider_services_available ON provider_services(is_available);

-- Bookings Indexes
CREATE INDEX IF NOT EXISTS idx_bookings_user ON bookings(user_id);
CREATE INDEX IF NOT EXISTS idx_bookings_provider ON bookings(provider_id);
CREATE INDEX IF NOT EXISTS idx_bookings_service ON bookings(service_id);
CREATE INDEX IF NOT EXISTS idx_bookings_date ON bookings(booking_date);
CREATE INDEX IF NOT EXISTS idx_bookings_status ON bookings(status);
CREATE INDEX IF NOT EXISTS idx_bookings_location ON bookings(service_city, service_state);
CREATE INDEX IF NOT EXISTS idx_bookings_created ON bookings(created_at);

-- Reviews Indexes
CREATE INDEX IF NOT EXISTS idx_reviews_provider ON reviews(provider_id);
CREATE INDEX IF NOT EXISTS idx_reviews_booking ON reviews(booking_id);
CREATE INDEX IF NOT EXISTS idx_reviews_rating ON reviews(rating DESC);
CREATE INDEX IF NOT EXISTS idx_reviews_verified ON reviews(is_verified, is_hidden);

-- Availability Indexes
CREATE INDEX IF NOT EXISTS idx_availability_provider ON provider_availability(provider_id);
CREATE INDEX IF NOT EXISTS idx_availability_day ON provider_availability(day_of_week);

-- Blocked Dates Indexes
CREATE INDEX IF NOT EXISTS idx_blocked_dates_provider ON provider_blocked_dates(provider_id);
CREATE INDEX IF NOT EXISTS idx_blocked_dates_date ON provider_blocked_dates(blocked_date);

-- =====================================================
-- 11. ROW LEVEL SECURITY (RLS) POLICIES
-- =====================================================

-- Enable RLS on all tables
ALTER TABLE user_profiles ENABLE ROW LEVEL SECURITY;
ALTER TABLE service_categories ENABLE ROW LEVEL SECURITY;
ALTER TABLE services ENABLE ROW LEVEL SECURITY;
ALTER TABLE service_providers ENABLE ROW LEVEL SECURITY;
ALTER TABLE provider_services ENABLE ROW LEVEL SECURITY;
ALTER TABLE bookings ENABLE ROW LEVEL SECURITY;
ALTER TABLE reviews ENABLE ROW LEVEL SECURITY;
ALTER TABLE provider_availability ENABLE ROW LEVEL SECURITY;
ALTER TABLE provider_blocked_dates ENABLE ROW LEVEL SECURITY;

-- User Profiles Policies
CREATE POLICY "Users can view own profile" ON user_profiles
    FOR SELECT TO authenticated
    USING (auth.uid() = id);

CREATE POLICY "Users can update own profile" ON user_profiles
    FOR UPDATE TO authenticated
    USING (auth.uid() = id);

CREATE POLICY "Users can insert own profile" ON user_profiles
    FOR INSERT TO authenticated
    WITH CHECK (auth.uid() = id);

-- Service Categories Policies (Public read, admin write)
CREATE POLICY "Anyone can view active categories" ON service_categories
    FOR SELECT TO authenticated, anon
    USING (is_active = true);

-- Services Policies (Public read, admin write)
CREATE POLICY "Anyone can view active services" ON services
    FOR SELECT TO authenticated, anon
    USING (is_active = true);

-- Service Providers Policies
CREATE POLICY "Anyone can view active providers" ON service_providers
    FOR SELECT TO authenticated, anon
    USING (is_active = true AND is_verified = true);

CREATE POLICY "Providers can manage own profile" ON service_providers
    FOR ALL TO authenticated
    USING (auth.uid() = user_id);

-- Provider Services Policies
CREATE POLICY "Anyone can view available provider services" ON provider_services
    FOR SELECT TO authenticated, anon
    USING (is_available = true);

CREATE POLICY "Providers can manage own services" ON provider_services
    FOR ALL TO authenticated
    USING (provider_id IN (SELECT id FROM service_providers WHERE user_id = auth.uid()));

-- Bookings Policies
CREATE POLICY "Users can view own bookings" ON bookings
    FOR SELECT TO authenticated
    USING (auth.uid() = user_id OR provider_id IN (SELECT id FROM service_providers WHERE user_id = auth.uid()));

CREATE POLICY "Users can create bookings" ON bookings
    FOR INSERT TO authenticated
    WITH CHECK (auth.uid() = user_id);

CREATE POLICY "Users and providers can update relevant bookings" ON bookings
    FOR UPDATE TO authenticated
    USING (auth.uid() = user_id OR provider_id IN (SELECT id FROM service_providers WHERE user_id = auth.uid()));

-- Reviews Policies
CREATE POLICY "Anyone can view non-hidden reviews" ON reviews
    FOR SELECT TO authenticated, anon
    USING (is_hidden = false);

CREATE POLICY "Users can create reviews for their bookings" ON reviews
    FOR INSERT TO authenticated
    WITH CHECK (auth.uid() = reviewer_id AND booking_id IN (SELECT id FROM bookings WHERE user_id = auth.uid()));

-- Provider Availability Policies
CREATE POLICY "Anyone can view provider availability" ON provider_availability
    FOR SELECT TO authenticated, anon
    USING (true);

CREATE POLICY "Providers can manage own availability" ON provider_availability
    FOR ALL TO authenticated
    USING (provider_id IN (SELECT id FROM service_providers WHERE user_id = auth.uid()));

-- Provider Blocked Dates Policies
CREATE POLICY "Providers can manage own blocked dates" ON provider_blocked_dates
    FOR ALL TO authenticated
    USING (provider_id IN (SELECT id FROM service_providers WHERE user_id = auth.uid()));

-- =====================================================
-- 12. FUNCTIONS AND TRIGGERS
-- =====================================================

-- Function to update updated_at timestamp
CREATE OR REPLACE FUNCTION update_updated_at_column()
RETURNS TRIGGER AS $$
BEGIN
    NEW.updated_at = NOW();
    RETURN NEW;
END;
$$ language 'plpgsql';

-- Apply updated_at triggers to all relevant tables
CREATE TRIGGER update_user_profiles_updated_at
    BEFORE UPDATE ON user_profiles
    FOR EACH ROW
    EXECUTE FUNCTION update_updated_at_column();

CREATE TRIGGER update_service_categories_updated_at
    BEFORE UPDATE ON service_categories
    FOR EACH ROW
    EXECUTE FUNCTION update_updated_at_column();

CREATE TRIGGER update_services_updated_at
    BEFORE UPDATE ON services
    FOR EACH ROW
    EXECUTE FUNCTION update_updated_at_column();

CREATE TRIGGER update_service_providers_updated_at
    BEFORE UPDATE ON service_providers
    FOR EACH ROW
    EXECUTE FUNCTION update_updated_at_column();

CREATE TRIGGER update_bookings_updated_at
    BEFORE UPDATE ON bookings
    FOR EACH ROW
    EXECUTE FUNCTION update_updated_at_column();

CREATE TRIGGER update_reviews_updated_at
    BEFORE UPDATE ON reviews
    FOR EACH ROW
    EXECUTE FUNCTION update_updated_at_column();

CREATE TRIGGER update_provider_availability_updated_at
    BEFORE UPDATE ON provider_availability
    FOR EACH ROW
    EXECUTE FUNCTION update_updated_at_column();

-- Function to automatically create user profile when user signs up
CREATE OR REPLACE FUNCTION public.handle_new_user()
RETURNS TRIGGER AS $$
BEGIN
    INSERT INTO public.user_profiles (id, email, email_verified)
    VALUES (NEW.id, NEW.email, NEW.email_confirmed_at IS NOT NULL);
    RETURN NEW;
END;
$$ language 'plpgsql' security definer;

-- Trigger to create profile on user signup
CREATE TRIGGER on_auth_user_created
    AFTER INSERT ON auth.users
    FOR EACH ROW EXECUTE FUNCTION public.handle_new_user();

-- Function to update provider rating when review is added/updated
CREATE OR REPLACE FUNCTION update_provider_rating()
RETURNS TRIGGER AS $$
BEGIN
    UPDATE service_providers
    SET
        rating_average = (
            SELECT ROUND(AVG(rating)::numeric, 2)
            FROM reviews
            WHERE provider_id = COALESCE(NEW.provider_id, OLD.provider_id)
            AND is_hidden = false
        ),
        rating_count = (
            SELECT COUNT(*)
            FROM reviews
            WHERE provider_id = COALESCE(NEW.provider_id, OLD.provider_id)
            AND is_hidden = false
        )
    WHERE id = COALESCE(NEW.provider_id, OLD.provider_id);

    RETURN COALESCE(NEW, OLD);
END;
$$ language 'plpgsql';

-- Triggers for rating updates
CREATE TRIGGER update_provider_rating_on_insert
    AFTER INSERT ON reviews
    FOR EACH ROW EXECUTE FUNCTION update_provider_rating();

CREATE TRIGGER update_provider_rating_on_update
    AFTER UPDATE ON reviews
    FOR EACH ROW EXECUTE FUNCTION update_provider_rating();

CREATE TRIGGER update_provider_rating_on_delete
    AFTER DELETE ON reviews
    FOR EACH ROW EXECUTE FUNCTION update_provider_rating();

-- Function to update booking count when booking status changes
CREATE OR REPLACE FUNCTION update_provider_booking_count()
RETURNS TRIGGER AS $$
BEGIN
    IF NEW.status = 'completed' AND (OLD.status IS NULL OR OLD.status != 'completed') THEN
        UPDATE service_providers
        SET total_bookings = total_bookings + 1
        WHERE id = NEW.provider_id;
    ELSIF OLD.status = 'completed' AND NEW.status != 'completed' THEN
        UPDATE service_providers
        SET total_bookings = GREATEST(total_bookings - 1, 0)
        WHERE id = NEW.provider_id;
    END IF;

    RETURN NEW;
END;
$$ language 'plpgsql';

-- Trigger for booking count updates
CREATE TRIGGER update_provider_booking_count_trigger
    AFTER UPDATE ON bookings
    FOR EACH ROW EXECUTE FUNCTION update_provider_booking_count();

-- =====================================================
-- 13. SAMPLE DATA
-- =====================================================

-- Insert sample service categories
INSERT INTO service_categories (name, description, icon, color, sort_order) VALUES
('Home Services', 'Cleaning, repairs, maintenance, and home improvement', 'fas fa-home', '#3B82F6', 1),
('Business Services', 'Consulting, accounting, legal, and professional services', 'fas fa-briefcase', '#10B981', 2),
('Tech Services', 'IT support, web development, and technical consulting', 'fas fa-laptop', '#8B5CF6', 3),
('Health & Wellness', 'Personal training, nutrition, therapy, and wellness', 'fas fa-heart', '#F59E0B', 4),
('Education & Training', 'Tutoring, coaching, and skill development', 'fas fa-graduation-cap', '#EF4444', 5),
('Creative Services', 'Design, photography, writing, and creative work', 'fas fa-palette', '#EC4899', 6)
ON CONFLICT (name) DO NOTHING;

-- Insert sample services
INSERT INTO services (category_id, name, description, short_description, price_type, base_price, duration_minutes) VALUES
(
    (SELECT id FROM service_categories WHERE name = 'Home Services'),
    'House Cleaning',
    'Professional house cleaning service including all rooms, bathrooms, and kitchen. We use eco-friendly products and bring all necessary equipment.',
    'Professional house cleaning with eco-friendly products',
    'hourly',
    45.00,
    120
),
(
    (SELECT id FROM service_categories WHERE name = 'Home Services'),
    'Plumbing Repair',
    'Expert plumbing services for leaks, clogs, installations, and emergency repairs. Licensed and insured plumbers.',
    'Expert plumbing repair and installation services',
    'hourly',
    85.00,
    60
),
(
    (SELECT id FROM service_categories WHERE name = 'Business Services'),
    'Business Consulting',
    'Strategic business consulting to help grow your business, improve operations, and increase profitability.',
    'Strategic business consulting and growth planning',
    'hourly',
    150.00,
    60
),
(
    (SELECT id FROM service_categories WHERE name = 'Tech Services'),
    'Web Development',
    'Custom website development, e-commerce solutions, and web application development using modern technologies.',
    'Custom website and web application development',
    'fixed',
    2500.00,
    NULL
),
(
    (SELECT id FROM service_categories WHERE name = 'Health & Wellness'),
    'Personal Training',
    'One-on-one personal training sessions customized to your fitness goals and current fitness level.',
    'Personalized fitness training and coaching',
    'hourly',
    75.00,
    60
)
ON CONFLICT DO NOTHING;

-- =====================================================
-- 14. USEFUL VIEWS
-- =====================================================

-- View for booking analytics
CREATE OR REPLACE VIEW booking_analytics AS
SELECT
    DATE_TRUNC('day', booking_date) as booking_day,
    s.name as service_name,
    sc.name as category_name,
    b.status,
    COUNT(*) as booking_count,
    AVG(b.final_price) as avg_price,
    SUM(b.final_price) as total_revenue
FROM bookings b
LEFT JOIN services s ON b.service_id = s.id
LEFT JOIN service_categories sc ON s.category_id = sc.id
GROUP BY DATE_TRUNC('day', booking_date), s.name, sc.name, b.status
ORDER BY booking_day DESC, booking_count DESC;

-- View for provider performance
CREATE OR REPLACE VIEW provider_performance AS
SELECT
    sp.id,
    sp.business_name,
    up.first_name || ' ' || up.last_name as provider_name,
    sp.rating_average,
    sp.rating_count,
    sp.total_bookings,
    COUNT(DISTINCT ps.service_id) as services_offered,
    AVG(CASE WHEN b.status = 'completed' THEN b.final_price END) as avg_booking_value,
    COUNT(CASE WHEN b.booking_date >= CURRENT_DATE - INTERVAL '30 days' THEN 1 END) as bookings_last_30_days
FROM service_providers sp
LEFT JOIN user_profiles up ON sp.user_id = up.id
LEFT JOIN provider_services ps ON sp.id = ps.provider_id
LEFT JOIN bookings b ON sp.id = b.provider_id
WHERE sp.is_active = true
GROUP BY sp.id, sp.business_name, up.first_name, up.last_name, sp.rating_average, sp.rating_count, sp.total_bookings
ORDER BY sp.rating_average DESC, sp.total_bookings DESC;

-- View for service popularity
CREATE OR REPLACE VIEW service_popularity AS
SELECT
    s.id,
    s.name,
    sc.name as category_name,
    COUNT(b.id) as total_bookings,
    COUNT(CASE WHEN b.status = 'completed' THEN 1 END) as completed_bookings,
    AVG(CASE WHEN r.rating IS NOT NULL THEN r.rating END) as avg_rating,
    COUNT(r.id) as review_count,
    AVG(s.base_price) as avg_price,
    COUNT(DISTINCT ps.provider_id) as provider_count
FROM services s
LEFT JOIN service_categories sc ON s.category_id = sc.id
LEFT JOIN bookings b ON s.id = b.service_id
LEFT JOIN reviews r ON b.id = r.booking_id
LEFT JOIN provider_services ps ON s.id = ps.service_id
WHERE s.is_active = true
GROUP BY s.id, s.name, sc.name
ORDER BY total_bookings DESC, avg_rating DESC;

-- View for user booking history
CREATE OR REPLACE VIEW user_booking_history AS
SELECT
    b.id,
    b.user_id,
    up.first_name || ' ' || up.last_name as customer_name,
    s.name as service_name,
    sc.name as category_name,
    sp.business_name as provider_name,
    b.booking_date,
    b.booking_time,
    b.status,
    b.final_price,
    b.created_at,
    r.rating,
    r.comment as review_comment
FROM bookings b
LEFT JOIN user_profiles up ON b.user_id = up.id
LEFT JOIN services s ON b.service_id = s.id
LEFT JOIN service_categories sc ON s.category_id = sc.id
LEFT JOIN service_providers sp ON b.provider_id = sp.id
LEFT JOIN reviews r ON b.id = r.booking_id
ORDER BY b.created_at DESC;

-- =====================================================
-- 15. COMMENTS FOR DOCUMENTATION
-- =====================================================

-- Table comments
COMMENT ON TABLE user_profiles IS 'Extended user profile information linked to auth.users';
COMMENT ON TABLE service_categories IS 'Categories for organizing different types of services';
COMMENT ON TABLE services IS 'Available services that can be booked';
COMMENT ON TABLE service_providers IS 'Users who provide services';
COMMENT ON TABLE provider_services IS 'Many-to-many relationship between providers and services';
COMMENT ON TABLE bookings IS 'Service booking requests and appointments';
COMMENT ON TABLE reviews IS 'Customer reviews and ratings for completed services';
COMMENT ON TABLE provider_availability IS 'Weekly availability schedule for service providers';
COMMENT ON TABLE provider_blocked_dates IS 'Specific dates when providers are unavailable';

-- Key column comments
COMMENT ON COLUMN bookings.status IS 'Booking status: pending, confirmed, in_progress, completed, cancelled, refunded';
COMMENT ON COLUMN reviews.rating IS 'Overall rating from 1-5 stars';
COMMENT ON COLUMN service_providers.rating_average IS 'Calculated average rating from all reviews';
COMMENT ON COLUMN services.price_type IS 'Pricing model: hourly, fixed, or custom';

-- =====================================================
-- 16. SUCCESS MESSAGE
-- =====================================================

SELECT 'ServicePro database setup completed successfully! 🎉' as status,
       'Tables created: ' || (
           SELECT COUNT(*) FROM information_schema.tables
           WHERE table_schema = 'public'
           AND table_name IN (
               'user_profiles', 'service_categories', 'services',
               'service_providers', 'provider_services', 'bookings',
               'reviews', 'provider_availability', 'provider_blocked_dates'
           )
       ) || '/9' as tables_created,
       'Sample data inserted for categories and services' as sample_data,
       'RLS policies enabled for security' as security,
       'Ready for user authentication and booking!' as next_steps;
