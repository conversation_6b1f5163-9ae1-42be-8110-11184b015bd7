-- Service Booking Database Setup for Supabase
-- Run this SQL in your Supabase SQL Editor

-- Create the bookings table
CREATE TABLE IF NOT EXISTS bookings (
    id UUID DEFAULT gen_random_uuid() PRIMARY KEY,
    user_id UUID REFERENCES auth.users(id) ON DELETE CASCADE NOT NULL,
    service_type VARCHAR(100) NOT NULL,
    booking_date DATE NOT NULL,
    booking_time TIME NOT NULL,
    notes TEXT,
    status VARCHAR(20) DEFAULT 'pending' CHECK (status IN ('pending', 'confirmed', 'cancelled', 'completed')),
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Add indexes for better performance
CREATE INDEX IF NOT EXISTS idx_bookings_user_id ON bookings(user_id);
CREATE INDEX IF NOT EXISTS idx_bookings_date ON bookings(booking_date);
CREATE INDEX IF NOT EXISTS idx_bookings_status ON bookings(status);
CREATE INDEX IF NOT EXISTS idx_bookings_created_at ON bookings(created_at);

-- Enable Row Level Security (RLS)
ALTER TABLE bookings ENABLE ROW LEVEL SECURITY;

-- Create policies for authenticated users
-- Users can only view their own bookings
CREATE POLICY "Users can view own bookings" ON bookings
    FOR SELECT TO authenticated
    USING (auth.uid() = user_id);

-- Users can only insert their own bookings
CREATE POLICY "Users can insert own bookings" ON bookings
    FOR INSERT TO authenticated
    WITH CHECK (auth.uid() = user_id);

-- Users can only update their own bookings
CREATE POLICY "Users can update own bookings" ON bookings
    FOR UPDATE TO authenticated
    USING (auth.uid() = user_id)
    WITH CHECK (auth.uid() = user_id);

-- Users can only delete their own bookings
CREATE POLICY "Users can delete own bookings" ON bookings
    FOR DELETE TO authenticated
    USING (auth.uid() = user_id);

-- Create function to update the updated_at timestamp
CREATE OR REPLACE FUNCTION update_updated_at_column()
RETURNS TRIGGER AS $$
BEGIN
    NEW.updated_at = NOW();
    RETURN NEW;
END;
$$ language 'plpgsql';

-- Create trigger to automatically update updated_at
CREATE TRIGGER update_bookings_updated_at
    BEFORE UPDATE ON bookings
    FOR EACH ROW
    EXECUTE FUNCTION update_updated_at_column();

-- Create a view for booking analytics (optional)
CREATE OR REPLACE VIEW booking_summary AS
SELECT
    service_type,
    status,
    COUNT(*) as booking_count,
    DATE_TRUNC('day', booking_date) as booking_day
FROM bookings
GROUP BY service_type, status, DATE_TRUNC('day', booking_date)
ORDER BY booking_day DESC, booking_count DESC;

-- Add comments for documentation
COMMENT ON TABLE bookings IS 'Stores service booking requests from authenticated users';
COMMENT ON COLUMN bookings.id IS 'Primary key, UUID';
COMMENT ON COLUMN bookings.user_id IS 'Reference to authenticated user';
COMMENT ON COLUMN bookings.service_type IS 'Type of service being booked';
COMMENT ON COLUMN bookings.booking_date IS 'Date of the service booking';
COMMENT ON COLUMN bookings.booking_time IS 'Time of the service booking';
COMMENT ON COLUMN bookings.notes IS 'Additional notes from the user';
COMMENT ON COLUMN bookings.status IS 'Current status of the booking';
COMMENT ON COLUMN bookings.created_at IS 'When the booking was created';
COMMENT ON COLUMN bookings.updated_at IS 'When the booking was last updated';

-- Success message
SELECT 'Service booking database setup completed successfully!' as status;
