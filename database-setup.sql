-- Contact Form Database Setup for Supabase
-- Run this SQL in your Supabase SQL Editor

-- Create the contact_submissions table
CREATE TABLE IF NOT EXISTS contact_submissions (
    id SERIAL PRIMARY KEY,
    first_name <PERSON><PERSON><PERSON><PERSON>(100) NOT NULL,
    last_name <PERSON><PERSON><PERSON><PERSON>(100) NOT NULL,
    email VARCHAR(255) NOT NULL,
    phone VARCHAR(20),
    company VARCHAR(255),
    subject VARCHAR(100) NOT NULL,
    message TEXT NOT NULL,
    newsletter_subscription BOOLEAN DEFAULT FALSE,
    submitted_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Add indexes for better performance
CREATE INDEX IF NOT EXISTS idx_contact_submissions_email ON contact_submissions(email);
CREATE INDEX IF NOT EXISTS idx_contact_submissions_created_at ON contact_submissions(created_at);
CREATE INDEX IF NOT EXISTS idx_contact_submissions_subject ON contact_submissions(subject);

-- Enable Row Level Security (RLS)
ALTER TABLE contact_submissions ENABLE ROW LEVEL SECURITY;

-- Create policy to allow anonymous users to insert data
-- This allows the form to submit data without authentication
CREATE POLICY "Allow anonymous inserts" ON contact_submissions
    FOR INSERT TO anon
    WITH CHECK (true);

-- Optional: Create policy to allow authenticated users to view their own submissions
-- Uncomment the following lines if you want users to be able to view their submissions
-- CREATE POLICY "Users can view own submissions" ON contact_submissions
--     FOR SELECT TO authenticated
--     USING (auth.uid() = user_id);

-- Optional: Add a user_id column if you want to associate submissions with authenticated users
-- ALTER TABLE contact_submissions ADD COLUMN user_id UUID REFERENCES auth.users(id);

-- Create a view for analytics (optional)
CREATE OR REPLACE VIEW contact_submissions_summary AS
SELECT 
    subject,
    COUNT(*) as submission_count,
    COUNT(CASE WHEN newsletter_subscription = true THEN 1 END) as newsletter_subscriptions,
    DATE_TRUNC('day', created_at) as submission_date
FROM contact_submissions 
GROUP BY subject, DATE_TRUNC('day', created_at)
ORDER BY submission_date DESC, submission_count DESC;

-- Grant access to the view for authenticated users (optional)
-- GRANT SELECT ON contact_submissions_summary TO authenticated;

-- Add comments for documentation
COMMENT ON TABLE contact_submissions IS 'Stores contact form submissions from the website';
COMMENT ON COLUMN contact_submissions.id IS 'Primary key, auto-incrementing';
COMMENT ON COLUMN contact_submissions.first_name IS 'Contact first name';
COMMENT ON COLUMN contact_submissions.last_name IS 'Contact last name';
COMMENT ON COLUMN contact_submissions.email IS 'Contact email address';
COMMENT ON COLUMN contact_submissions.phone IS 'Optional phone number';
COMMENT ON COLUMN contact_submissions.company IS 'Optional company name';
COMMENT ON COLUMN contact_submissions.subject IS 'Form subject category';
COMMENT ON COLUMN contact_submissions.message IS 'Contact message content';
COMMENT ON COLUMN contact_submissions.newsletter_subscription IS 'Whether user opted for newsletter';
COMMENT ON COLUMN contact_submissions.submitted_at IS 'When the form was submitted (user timezone)';
COMMENT ON COLUMN contact_submissions.created_at IS 'When the record was created (server timezone)';

-- Success message
SELECT 'Contact form database setup completed successfully!' as status;
