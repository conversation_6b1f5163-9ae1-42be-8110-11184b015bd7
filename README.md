# Beautiful Contact Form with Supabase Integration

A modern, responsive contact form built with HTML, CSS, and vanilla JavaScript, integrated with Supabase for data storage.

## Features

- 🎨 Beautiful, modern design with gradient backgrounds and animations
- 📱 Fully responsive design that works on all devices
- ✅ Real-time form validation with user-friendly error messages
- 🚀 Smooth animations and hover effects
- 📊 Supabase integration for data storage
- 🔄 Loading states and success/error feedback
- 📧 Newsletter subscription option
- 🎯 Accessible form with proper labels and ARIA attributes

## Setup Instructions

### 1. Create a Supabase Project

1. Go to [Supabase](https://supabase.com) and create a new account if you don't have one
2. Create a new project
3. Wait for the project to be set up (this may take a few minutes)

### 2. Get Your Supabase Credentials

1. In your Supabase dashboard, go to Settings > API
2. Copy your Project URL and anon/public key
3. Update the `script.js` file with your credentials:

```javascript
const SUPABASE_URL = 'your-project-url-here';
const SUPABASE_ANON_KEY = 'your-anon-key-here';
```

### 3. Create the Database Table

In your Supabase dashboard, go to the SQL Editor and run this query to create the contact submissions table:

```sql
CREATE TABLE contact_submissions (
    id SERIAL PRIMARY KEY,
    first_name VARCHAR(100) NOT NULL,
    last_name VARCHAR(100) NOT NULL,
    email VARCHAR(255) NOT NULL,
    phone VARCHAR(20),
    company VARCHAR(255),
    subject VARCHAR(100) NOT NULL,
    message TEXT NOT NULL,
    newsletter_subscription BOOLEAN DEFAULT FALSE,
    submitted_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);
```

### 4. Set Up Row Level Security (Optional but Recommended)

For security, you can enable Row Level Security on your table:

```sql
ALTER TABLE contact_submissions ENABLE ROW LEVEL SECURITY;

-- Allow anonymous users to insert data
CREATE POLICY "Allow anonymous inserts" ON contact_submissions
    FOR INSERT TO anon
    WITH CHECK (true);
```

### 5. Run the Application

1. Open `index.html` in your web browser
2. Fill out the form and test the submission
3. Check your Supabase dashboard to see the submitted data

## Form Fields

- **First Name** (required): User's first name
- **Last Name** (required): User's last name  
- **Email** (required): Valid email address
- **Phone** (optional): Phone number
- **Company** (optional): Company name
- **Subject** (required): Dropdown with predefined options
- **Message** (required): Text area for detailed message
- **Newsletter** (optional): Checkbox for newsletter subscription

## File Structure

```
├── index.html          # Main HTML file with form structure
├── styles.css          # CSS styles for beautiful design
├── script.js           # JavaScript for form functionality and Supabase integration
└── README.md           # This file
```

## Browser Support

This application works in all modern browsers including:
- Chrome 60+
- Firefox 60+
- Safari 12+
- Edge 79+

## Customization

You can easily customize the form by:
- Modifying the CSS variables for colors and spacing
- Adding or removing form fields in the HTML
- Updating the validation rules in `script.js`
- Changing the form styling in `styles.css`

## Troubleshooting

### Common Issues

1. **Form not submitting**: Check that your Supabase credentials are correct
2. **Database errors**: Ensure the table is created with the correct schema
3. **Validation errors**: Check the browser console for JavaScript errors

### Support

If you encounter any issues, check:
1. Browser console for JavaScript errors
2. Supabase dashboard for database connection issues
3. Network tab in developer tools for API request failures
